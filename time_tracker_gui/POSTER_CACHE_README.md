# Poster Cache Feature

## Overview

The Time Tracker GUI now includes an intelligent poster caching system for local games. This feature automatically fetches game posters from the RAWG Video Games Database API and caches them locally to reduce API costs and improve performance.

## Features

### 🎮 Automatic Poster Fetching
- Automatically searches for game posters based on local game names
- Uses the RAWG API to find matching game artwork
- Cleans up game names (removes .exe extensions, normalizes whitespace) for better search results

### 💾 Smart Caching
- **Local Storage**: Posters are cached in the device's documents directory
- **Metadata Caching**: Game information is stored in SharedPreferences
- **Image Caching**: Actual poster images are saved as local files
- **Cache Expiry**: Cached data automatically expires after 7 days

### ⚡ Performance Optimization
- **Batch Processing**: Games are processed in batches of 5 to avoid overwhelming the API
- **Rate Limiting**: 500ms delay between batches to be respectful to the API
- **Fallback System**: Gracefully falls back to placeholders if posters aren't available
- **Local-First**: Always checks cache before making API requests

### 🛠️ Cache Management
- **Cache Statistics**: View cache size and storage information
- **Manual Refresh**: Force refresh cached data
- **Clear Cache**: Remove all cached posters and images
- **Settings Integration**: Manage cache through the settings dialog

## How It Works

### 1. Game Name Processing
```dart
String _getGameName(AppModel app) {
  String gameName = app.productName ?? app.name ?? '';
  
  // Clean up the game name for better search results
  gameName = gameName
      .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '') // Remove .exe
      .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
      .trim();
  
  return gameName;
}
```

### 2. Cache Structure
- **Metadata**: Stored in SharedPreferences with key pattern `cached_poster_{sanitized_game_name}`
- **Images**: Stored as files with pattern `poster_image_{sanitized_game_name}.jpg`
- **Expiry**: Each cache entry includes a timestamp for automatic expiration

### 3. API Integration
- Uses the existing RAWG API service
- Searches for games by name with a limit of 1 result per game
- Caches both the poster metadata and the actual image file

## Usage

### For Users
1. **Automatic**: Posters are automatically fetched when viewing the local game library
2. **Settings**: Access cache management through Settings → Cache tab
3. **Manual Control**: Refresh or clear cache as needed

### For Developers
```dart
// Get a cached poster for a game
final poster = await ref.read(cachedPosterProvider('Game Name').future);

// Get cached image path
final imagePath = await ref.read(cachedImagePathProvider('Game Name').future);

// Clear all cache
await ref.read(clearCacheProvider.future);
```

## Cache Settings

The cache settings widget provides:

- **Cache Statistics**: Current cache size and storage location
- **Refresh Cache**: Invalidate providers to force refresh
- **Clear Cache**: Remove all cached data with confirmation dialog
- **Automatic Management**: 7-day expiry with automatic cleanup

## Benefits

### 🔄 Reduced API Costs
- Posters are only fetched once per game
- 7-day cache duration reduces repeated API calls
- Batch processing prevents API rate limiting

### 🚀 Improved Performance
- Local images load instantly
- No network requests for cached content
- Graceful fallbacks for missing data

### 🎨 Enhanced User Experience
- Beautiful game posters instead of generic placeholders
- Consistent visual experience
- Smooth loading with progress indicators

## Technical Implementation

### Services
- `PosterCacheService`: Core caching logic
- `PosterService`: RAWG API integration (existing)

### Providers
- `posterCacheServiceProvider`: Service instance
- `cachedPosterProvider`: Individual game poster data
- `cachedImagePathProvider`: Local image file paths
- `cacheStatsProvider`: Cache statistics
- `clearCacheProvider`: Cache clearing functionality

### Widgets
- `LocalGamePosterWidget`: Updated to use cached posters
- `CacheSettingsWidget`: Cache management interface
- `SettingsDialog`: Updated with cache tab

### Models
- Uses existing `Poster` model from RAWG integration
- Extends `AppModel` functionality for game name extraction

## Future Enhancements

- **Configurable Cache Duration**: Allow users to set custom expiry times
- **Cache Size Limits**: Implement maximum cache size with LRU eviction
- **Offline Mode**: Better handling of offline scenarios
- **Bulk Operations**: Batch fetch posters for all games at once
- **Image Optimization**: Compress cached images to save space 
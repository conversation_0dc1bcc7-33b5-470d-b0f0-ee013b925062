// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:time_tracker_gui/main.dart';

void main() {
  testWidgets('Time Tracker App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TimeTrackerApp());

    // Verify that the app loads with the correct title
    expect(find.text('Time Tracker'), findsOneWidget);

    // Verify that the dashboard tab is visible
    expect(find.text('Dashboard'), findsOneWidget);
  });
}

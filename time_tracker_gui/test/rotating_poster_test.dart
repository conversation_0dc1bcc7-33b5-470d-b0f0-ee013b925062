import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:time_tracker_gui/presentation/providers/rotating_poster_provider.dart';
import 'package:time_tracker_gui/data/models/app_model.dart';
import 'package:time_tracker_gui/data/models/poster_model.dart';

void main() {
  group('RotatingPosterProvider Tests', () {
    test('RotatingPosterState copyWith works correctly', () {
      const initialState = RotatingPosterState(isLoading: true);
      
      final poster = Poster(
        id: 1,
        name: 'Test Game',
        backgroundImage: 'https://example.com/image.jpg',
      );
      
      final app = AppModel(id: 1, name: 'Test Game');
      
      final newState = initialState.copyWith(
        currentPoster: poster,
        currentApp: app,
        isLoading: false,
      );
      
      expect(newState.currentPoster, equals(poster));
      expect(newState.currentApp, equals(app));
      expect(newState.isLoading, equals(false));
    });

    test('RotatingPosterState maintains immutability', () {
      const initialState = RotatingPosterState(isLoading: true);
      
      final newState = initialState.copyWith(isLoading: false);
      
      expect(initialState.isLoading, equals(true));
      expect(newState.isLoading, equals(false));
    });

    test('RotatingPosterNotifier initializes correctly', () {
      final container = ProviderContainer();
      
      final notifier = container.read(rotatingPosterProvider.notifier);
      final state = container.read(rotatingPosterProvider);

      expect(state.isLoading, equals(true));
      expect(notifier, isA<RotatingPosterNotifier>());
      
      // Properly dispose to clean up timer
      container.dispose();
    });

    test('RotatingPosterState handles empty poster correctly', () {
      const stateWithoutPoster = RotatingPosterState(
        currentPoster: null,
        currentApp: null,
        isLoading: false,
      );
      
      expect(stateWithoutPoster.currentPoster, isNull);
      expect(stateWithoutPoster.currentApp, isNull);
      expect(stateWithoutPoster.isLoading, isFalse);
    });

    test('RotatingPosterState copyWith preserves null values correctly', () {
      const initialState = RotatingPosterState(
        currentPoster: null,
        currentApp: null,
        isLoading: false,
      );
      
      final newState = initialState.copyWith(isLoading: true);
      
      expect(newState.currentPoster, isNull);
      expect(newState.currentApp, isNull);
      expect(newState.isLoading, isTrue);
    });
  });
} 
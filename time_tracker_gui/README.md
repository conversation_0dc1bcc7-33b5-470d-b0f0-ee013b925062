# Time Tracker GUI

A comprehensive Flutter GUI application for the Time Tracker service, providing real-time monitoring and management of application usage across multiple platforms.

## Features

### 🎯 Core Functionality
- **Real-time Tracking**: Live monitoring of application usage with WebSocket communication
- **Multi-platform Support**: Runs on Android, iOS, Windows, macOS, and Linux
- **Responsive Design**: Adaptive UI that works seamlessly on mobile, tablet, and desktop
- **Dark/Light Theme**: Toggle between themes with system preference support

### 📊 Data Visualization
- **Interactive Charts**: Bar charts, pie charts, and line graphs for usage statistics
- **Dashboard Overview**: Quick stats and current tracking status
- **Timeline View**: Historical usage patterns and trends
- **App Statistics**: Detailed metrics for each tracked application

### 🎮 User Interface
- **Modern Material Design 3**: Clean, intuitive interface following Google's design guidelines
- **Smooth Animations**: Fluid transitions and micro-interactions
- **Accessibility Support**: Screen reader compatible and keyboard navigation
- **Responsive Layout**: Adapts to different screen sizes and orientations

### 🔧 Technical Features
- **State Management**: Riverpod for clean, testable state management
- **Error Handling**: Comprehensive error states and loading indicators
- **Offline Support**: Graceful handling of network connectivity issues
- **Real-time Updates**: Live data synchronization with the backend service

## Architecture

### Communication Protocol
The application uses a hybrid communication approach:
- **HTTP REST API**: For CRUD operations and data fetching
- **WebSocket**: For real-time updates and live tracking status
- **Cross-platform Compatibility**: Works seamlessly across all Flutter-supported platforms

### Project Structure
```
lib/
├── main.dart                    # App entry point
├── app/
│   └── theme/                   # App theming
├── core/
│   └── constants/               # App constants and configuration
├── data/
│   ├── models/                  # Data models
│   ├── repositories/            # Repository pattern implementation
│   └── services/                # API and WebSocket services
├── presentation/
│   ├── pages/                   # UI screens
│   ├── widgets/                 # Reusable UI components
│   └── providers/               # State management
└── test/                        # Unit and widget tests
```

### State Management
- **Riverpod**: Modern, compile-safe state management
- **Clean Architecture**: Separation of concerns with clear data flow
- **Reactive Programming**: Stream-based real-time updates

class TimeUtil {
  static String formatMinutes(int minutes) {
    if (minutes == 0) return '0m';
    
    return minutes < 60
      ? '${minutes}m'
      : '${minutes ~/ 60}h ${minutes % 60}m';
  }

  static String formatSeconds(int seconds) {
    if (seconds == 0) return '0s';
    
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${secs}s';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }

  static String formatDuration(int seconds) {
    if (seconds == 0) return '0s';
    
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }

  static String formatDurationFromMinutes(int minutes) {
    if (minutes == 0) return '0m';
    
    final hours = minutes ~/ 60;
    final mins = minutes % 60;

    if (hours > 0) {
      return '${hours}h ${mins}m';
    } else {
      return '${mins}m';
    }
  }

  static int secondsToMinutes(int seconds) {
    return (seconds / 60).round();
  }

  static int minutesToSeconds(int minutes) {
    return minutes * 60;
  }
}
import '../models/app_model.dart';
import '../services/mock_backend_service.dart';
import 'time_tracker_repository.dart';

class MockTimeTrackerRepository implements TimeTrackerRepository {
  final MockBackendService _mockService = MockBackendService();

  @override
  Future<List<AppModel>> getAllApps() => _mockService.getAllApps();

  @override
  Future<AppModel?> getAppByName(String name) => _mockService.getAppByName(name);

  @override
  Future<void> insertApp(String name) => _mockService.insertApp(name);

  @override
  Future<void> deleteApp(int appId) => _mockService.deleteApp(appId);

  @override
  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) => _mockService.getTimeline(
    startDate: startDate,
    endDate: endDate,
    appId: appId,
  );

  @override
  Future<Map<int, int>> getSessionCounts() async {
    // Get all timeline data and count sessions per app
    final timeline = await _mockService.getTimeline();
    final Map<int, int> sessionCounts = {};

    for (final session in timeline) {
      sessionCounts[session.appId] = (sessionCounts[session.appId] ?? 0) + 1;
    }

    return sessionCounts;
  }

  @override
  Future<TrackingStatus> getTrackingStatus() => _mockService.getTrackingStatus();

  @override
  Future<void> startTracking() => _mockService.startTracking();

  @override
  Future<void> stopTracking() => _mockService.stopTracking();

  @override
  Future<void> pauseTracking() => _mockService.pauseTracking();

  @override
  Future<void> resumeTracking() => _mockService.resumeTracking();

  @override
  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) => _mockService.getStatistics();

  @override
  Stream<TrackingStatus> get trackingStatusStream => _mockService.trackingStatusStream;

  @override
  Stream<AppModel> get appUpdateStream => _mockService.appUpdateStream;

  @override
  Stream<Map<String, dynamic>> get sessionUpdateStream => const Stream.empty();

  // Checkpoint methods
  @override
  Future<List<CheckpointModel>> getCheckpoints(int appId) async {
    return [
      CheckpointModel(
        id: 1,
        name: 'Work Session',
        description: 'Focus work time',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        validFrom: DateTime.now().subtract(const Duration(days: 1)),
        color: '#FF5722',
        appId: appId,
      ),
      CheckpointModel(
        id: 2,
        name: 'Break Time',
        description: 'Rest and relaxation',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        validFrom: DateTime.now().subtract(const Duration(hours: 2)),
        color: '#4CAF50',
        appId: appId,
      ),
    ];
  }

  @override
  Future<CheckpointModel?> getActiveCheckpoint(int appId) async {
    final checkpoints = await getCheckpoints(appId);
    return checkpoints.isNotEmpty ? checkpoints.first : null;
  }

  @override
  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color, int appId) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Future<void> setActiveCheckpoint(int checkpointId, int appId) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<void> clearActiveCheckpoint(int appId) async {
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  Future<void> deleteCheckpoint(int checkpointId, int appId) async {
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Future<List<CheckpointDurationModel>> getCheckpointStats(int checkpointId) async {
    // Return mock checkpoint stats
    return [
      CheckpointDurationModel(
        id: 1,
        checkpointId: checkpointId,
        appId: 1,
        duration: 120,
        sessionsCount: 5,
        lastUpdated: DateTime.now(),
      ),
      CheckpointDurationModel(
        id: 2,
        checkpointId: checkpointId,
        appId: 2,
        duration: 85,
        sessionsCount: 3,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  @override
  Stream<List<CheckpointModel>> get checkpointsStream => Stream.periodic(
    const Duration(seconds: 5),
    (_) => getCheckpoints(0),
  ).asyncMap((future) => future);

  @override
  Stream<CheckpointModel?> get activeCheckpointStream => Stream.periodic(
    const Duration(seconds: 5),
    (_) => getActiveCheckpoint(0),
  ).asyncMap((future) => future);

  @override
  Future<void> connect() async {
    // Start the mock session for demonstration
    _mockService.startMockSession();
  }

  @override
  Future<void> disconnect() async {
    _mockService.dispose();
  }
}

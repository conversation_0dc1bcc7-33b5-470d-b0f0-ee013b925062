import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/poster_model.dart';
import '../../core/constants/api_constants.dart';

class PosterService {
  // Fetch popular/trending games
  Future<List<Poster>> fetchPosters({int page = 1, int pageSize = 20}) async {
    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'page': page.toString(),
        'page_size': pageSize.toString(),
        'ordering': '-rating', // Order by rating descending
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        return results.map((poster) => Poster.fromJson(poster)).toList();
      } else {
        throw Exception('Failed to load games: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch games: $e');
    }
  }

  // Search games by name
  Future<List<Poster>> searchGames(String query, {int page = 1, int pageSize = 20}) async {
    if (query.trim().isEmpty) {
      return fetchPosters(page: page, pageSize: pageSize);
    }

    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgSearchEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'search': query.trim(),
        'page': page.toString(),
        'page_size': pageSize.toString(),
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        return results.map((poster) => Poster.fromJson(poster)).toList();
      } else {
        throw Exception('Failed to search games: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to search games: $e');
    }
  }

  // Get games by specific genre
  Future<List<Poster>> getGamesByGenre(String genreId, {int page = 1, int pageSize = 20}) async {
    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'genres': genreId,
        'page': page.toString(),
        'page_size': pageSize.toString(),
        'ordering': '-rating',
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        return results.map((poster) => Poster.fromJson(poster)).toList();
      } else {
        throw Exception('Failed to load games by genre: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch games by genre: $e');
    }
  }

  // Get game details by ID
  Future<Poster?> getGameById(int gameId) async {
    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}/$gameId')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        return Poster.fromJson(jsonResponse);
      } else if (response.statusCode == 404) {
        return null; // Game not found
      } else {
        throw Exception('Failed to load game details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch game details: $e');
    }
  }
} 
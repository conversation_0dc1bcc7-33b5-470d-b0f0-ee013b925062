import 'dart:async';
import 'dart:math';
import '../models/app_model.dart';

class MockBackendService {
  static final MockBackendService _instance = MockBackendService._internal();
  factory MockBackendService() => _instance;
  MockBackendService._internal();

  final List<AppModel> _mockApps = [
    AppModel(
      id: 1,
      name: 'Visual Studio Code',
      productName: 'Code',
      duration: 480, // 8 hours
      launches: 15,
      longestSession: 180, // 3 hours
      longestSessionOn: DateTime.now().subtract(const Duration(days: 2)),
    ),
    AppModel(
      id: 2,
      name: 'Google Chrome',
      productName: 'Chrome',
      duration: 360, // 6 hours
      launches: 25,
      longestSession: 120, // 2 hours
      longestSessionOn: DateTime.now().subtract(const Duration(days: 1)),
    ),
    AppModel(
      id: 3,
      name: 'Slack',
      productName: 'Slack',
      duration: 240, // 4 hours
      launches: 8,
      longestSession: 90, // 1.5 hours
      longestSessionOn: DateTime.now().subtract(const Duration(days: 3)),
    ),
    AppModel(
      id: 4,
      name: 'Terminal',
      productName: 'Terminal',
      duration: 180, // 3 hours
      launches: 12,
      longestSession: 60, // 1 hour
      longestSessionOn: DateTime.now().subtract(const Duration(days: 1)),
    ),
    AppModel(
      id: 5,
      name: 'Firefox',
      productName: 'Firefox',
      duration: 120, // 2 hours
      launches: 5,
      longestSession: 45, // 45 minutes
      longestSessionOn: DateTime.now().subtract(const Duration(days: 4)),
    ),
  ];

  TrackingStatus _currentStatus = const TrackingStatus(
    isTracking: true,
    isPaused: false,
    currentApp: 'Visual Studio Code',
    currentSessionDuration: 1800, // 30 minutes
    sessionStartTime: null,
  );

  final StreamController<TrackingStatus> _statusController = StreamController.broadcast();
  final StreamController<AppModel> _appUpdateController = StreamController.broadcast();
  
  Timer? _sessionTimer;

  Stream<TrackingStatus> get trackingStatusStream => _statusController.stream;
  Stream<AppModel> get appUpdateStream => _appUpdateController.stream;

  void startMockSession() {
    _currentStatus = _currentStatus.copyWith(
      sessionStartTime: DateTime.now(),
    );
    
    _sessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_currentStatus.isPaused && _currentStatus.isTracking) {
        _currentStatus = _currentStatus.copyWith(
          currentSessionDuration: _currentStatus.currentSessionDuration + 1,
        );
        _statusController.add(_currentStatus);
      }
    });
  }

  Future<List<AppModel>> getAllApps() async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    return List.from(_mockApps);
  }

  Future<AppModel?> getAppByName(String name) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockApps.firstWhere(
      (app) => app.name?.toLowerCase() == name.toLowerCase(),
      orElse: () => throw Exception('App not found'),
    );
  }

  Future<void> insertApp(String name) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final newApp = AppModel(
      id: _mockApps.length + 1,
      name: name,
      duration: 0,
      launches: 0,
      longestSession: 0,
    );
    _mockApps.add(newApp);
    _appUpdateController.add(newApp);
  }

  Future<void> deleteApp(int appId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    _mockApps.removeWhere((app) => app.id == appId);
  }

  Future<TrackingStatus> getTrackingStatus() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _currentStatus;
  }

  Future<void> startTracking() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _currentStatus = _currentStatus.copyWith(
      isTracking: true,
      isPaused: false,
      sessionStartTime: DateTime.now(),
      currentSessionDuration: 0,
    );
    startMockSession();
    _statusController.add(_currentStatus);
  }

  Future<void> stopTracking() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _sessionTimer?.cancel();
    _currentStatus = _currentStatus.copyWith(
      isTracking: false,
      isPaused: false,
      currentApp: null,
      currentSessionDuration: 0,
      sessionStartTime: null,
    );
    _statusController.add(_currentStatus);
  }

  Future<void> pauseTracking() async {
    await Future.delayed(const Duration(milliseconds: 200));
    _currentStatus = _currentStatus.copyWith(isPaused: true);
    _statusController.add(_currentStatus);
  }

  Future<void> resumeTracking() async {
    await Future.delayed(const Duration(milliseconds: 200));
    _currentStatus = _currentStatus.copyWith(isPaused: false);
    _statusController.add(_currentStatus);
  }

  Future<List<AppStatistics>> getStatistics() async {
    await Future.delayed(const Duration(milliseconds: 600));
    
    final random = Random();
    return _mockApps.map((app) {
      return AppStatistics(
        app: app,
        totalDuration: app.duration ?? 0,
        todayDuration: random.nextInt(120), // Random today usage
        weekDuration: (app.duration ?? 0) + random.nextInt(200),
        monthDuration: (app.duration ?? 0) + random.nextInt(500),
        averageSessionLength: ((app.longestSession ?? 0) * 0.7),
        recentSessions: [], // Empty for now
      );
    }).toList();
  }

  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Generate mock timeline data
    final timeline = <TimelineModel>[];
    final random = Random();
    
    for (int i = 0; i < 30; i++) {
      final date = DateTime.now().subtract(Duration(days: i));
      for (final app in _mockApps.take(3)) {
        timeline.add(TimelineModel(
          id: timeline.length + 1,
          date: date,
          duration: random.nextInt(120) + 30, // 30-150 minutes
          appId: app.id,
        ));
      }
    }
    
    return timeline;
  }

  void dispose() {
    _sessionTimer?.cancel();
    _statusController.close();
    _appUpdateController.close();
  }
}

extension TrackingStatusCopyWith on TrackingStatus {
  TrackingStatus copyWith({
    bool? isTracking,
    bool? isPaused,
    String? currentApp,
    int? currentSessionDuration,
    DateTime? sessionStartTime,
  }) {
    return TrackingStatus(
      isTracking: isTracking ?? this.isTracking,
      isPaused: isPaused ?? this.isPaused,
      currentApp: currentApp ?? this.currentApp,
      currentSessionDuration: currentSessionDuration ?? this.currentSessionDuration,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
    );
  }
}

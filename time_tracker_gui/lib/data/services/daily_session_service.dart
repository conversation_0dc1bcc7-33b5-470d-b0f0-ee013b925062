import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class DailySessionData {
  final String date;
  final int totalMinutes;
  final List<SessionEntry> sessions;

  const DailySessionData({
    required this.date,
    required this.totalMinutes,
    required this.sessions,
  });

  factory DailySessionData.fromJson(Map<String, dynamic> json) {
    return DailySessionData(
      date: json['date'] as String,
      totalMinutes: json['totalMinutes'] as int,
      sessions: (json['sessions'] as List<dynamic>)
          .map((e) => SessionEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'totalMinutes': totalMinutes,
      'sessions': sessions.map((e) => e.toJson()).toList(),
    };
  }
}

class SessionEntry {
  final String appName;
  final int durationMinutes;
  final DateTime startTime;

  const SessionEntry({
    required this.appName,
    required this.durationMinutes,
    required this.startTime,
  });

  factory SessionEntry.fromJson(Map<String, dynamic> json) {
    return SessionEntry(
      appName: json['appName'] as String,
      durationMinutes: json['durationMinutes'] as int,
      startTime: DateTime.parse(json['startTime'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'appName': appName,
      'durationMinutes': durationMinutes,
      'startTime': startTime.toIso8601String(),
    };
  }
}

class DailySessionService {
  static const String _dailySessionPrefix = 'daily_session_';

  static String _getTodayKey() {
    final today = DateTime.now();
    return '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
  }

  static String _getDailySessionKey(String date) {
    return '$_dailySessionPrefix$date';
  }

  static Future<DailySessionData> getTodaySessionData() async {
    final today = _getTodayKey();
    return getDailySessionData(today);
  }

  static Future<DailySessionData> getDailySessionData(String date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getDailySessionKey(date);
      final jsonString = prefs.getString(key);

      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return DailySessionData.fromJson(json);
      }
    } catch (e) {
      print('Error loading daily session data for $date: $e');
    }

    return DailySessionData(
      date: date,
      totalMinutes: 0,
      sessions: [],
    );
  }

  static Future<void> addSession({
    required String appName,
    required int durationMinutes,
    required DateTime startTime,
  }) async {
    final today = _getTodayKey();
    final currentData = await getDailySessionData(today);

    final newSession = SessionEntry(
      appName: appName,
      durationMinutes: durationMinutes,
      startTime: startTime,
    );

    final updatedData = DailySessionData(
      date: today,
      totalMinutes: currentData.totalMinutes + durationMinutes,
      sessions: [...currentData.sessions, newSession],
    );

    await _saveDailySessionData(updatedData);
  }

  static Future<void> _saveDailySessionData(DailySessionData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getDailySessionKey(data.date);
      final jsonString = jsonEncode(data.toJson());
      await prefs.setString(key, jsonString);
    } catch (e) {
      print('Error saving daily session data: $e');
    }
  }

  static Future<List<DailySessionData>> getLastNDaysData(int days) async {
    final List<DailySessionData> result = [];
    final now = DateTime.now();

    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      final data = await getDailySessionData(dateKey);
      result.add(data);
    }

    return result;
  }

  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_dailySessionPrefix)) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      print('Error clearing session data: $e');
    }
  }
}
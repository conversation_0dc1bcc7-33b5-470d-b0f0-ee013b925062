import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/poster_model.dart';
import 'poster_service.dart';

class PosterCacheService {
  static const String _cacheKeyPrefix = 'cached_poster_';
  static const String _imageCachePrefix = 'poster_image_';
  static const Duration _cacheExpiry = Duration(days: 7); // Cache for 7 days
  
  final PosterService _posterService = PosterService();

  // Get cached poster data for a game name
  Future<Poster?> getCachedPoster(String gameName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(gameName);
      final cachedData = prefs.getString(cacheKey);
      
      if (cachedData != null) {
        final Map<String, dynamic> data = json.decode(cachedData);
        final cachedTime = DateTime.parse(data['cachedAt']);
        
        // Check if cache is still valid
        if (DateTime.now().difference(cachedTime) < _cacheExpiry) {
          return Poster.fromJson(data['poster']);
        } else {
          // Cache expired, remove it
          await _removeCachedPoster(gameName);
        }
      }
    } catch (e) {
      print('Error getting cached poster for $gameName: $e');
    }
    return null;
  }

  // Cache poster data for a game
  Future<void> cachePoster(String gameName, Poster poster) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(gameName);
      
      final cacheData = {
        'poster': poster.toJson(),
        'cachedAt': DateTime.now().toIso8601String(),
      };
      
      await prefs.setString(cacheKey, json.encode(cacheData));
    } catch (e) {
      print('Error caching poster for $gameName: $e');
    }
  }

  // Search for a game poster and cache it
  Future<Poster?> searchAndCachePoster(String gameName) async {
    try {
      // First check cache
      final cachedPoster = await getCachedPoster(gameName);
      if (cachedPoster != null) {
        return cachedPoster;
      }

      // Search for the game using RAWG API
      final searchResults = await _posterService.searchGames(gameName, pageSize: 1);

      if (searchResults.isNotEmpty) {
        final poster = searchResults.first;

        // Cache the result
        await cachePoster(gameName, poster);

        // Also cache the image if available
        if (poster.hasImage) {
          await _cacheImage(poster.backgroundImage, gameName);
        }

        return poster;
      }
    } catch (e) {
      print('Error searching and caching poster for $gameName: $e');
    }
    return null;
  }

  // Fast search - only cache poster data, skip image caching for speed
  Future<Poster?> searchAndCachePosterFast(String gameName) async {
    try {
      // First check cache
      final cachedPoster = await getCachedPoster(gameName);
      if (cachedPoster != null) {
        return cachedPoster;
      }

      // Search for the game using RAWG API
      final searchResults = await _posterService.searchGames(gameName, pageSize: 1);

      if (searchResults.isNotEmpty) {
        final poster = searchResults.first;

        // Cache only the poster data (skip image caching for speed)
        await cachePoster(gameName, poster);

        // Cache image in background without waiting
        if (poster.hasImage) {
          _cacheImage(poster.backgroundImage, gameName).catchError((e) {
            print('Background image caching failed for $gameName: $e');
          });
        }

        return poster;
      }
    } catch (e) {
      print('Error fast searching poster for $gameName: $e');
    }
    return null;
  }

  // Get cached image file path
  Future<String?> getCachedImagePath(String gameName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageFile = File('${directory.path}/${_getImageCacheKey(gameName)}.jpg');
      
      if (await imageFile.exists()) {
        return imageFile.path;
      }
    } catch (e) {
      print('Error getting cached image path for $gameName: $e');
    }
    return null;
  }

  // Cache image from URL
  Future<void> _cacheImage(String imageUrl, String gameName) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final imageFile = File('${directory.path}/${_getImageCacheKey(gameName)}.jpg');
        await imageFile.writeAsBytes(response.bodyBytes);
      }
    } catch (e) {
      print('Error caching image for $gameName: $e');
    }
  }

  // Remove cached poster data
  Future<void> _removeCachedPoster(String gameName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(gameName);
      await prefs.remove(cacheKey);
      
      // Also remove cached image
      await _removeCachedImage(gameName);
    } catch (e) {
      print('Error removing cached poster for $gameName: $e');
    }
  }

  // Remove cached image
  Future<void> _removeCachedImage(String gameName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageFile = File('${directory.path}/${_getImageCacheKey(gameName)}.jpg');
      
      if (await imageFile.exists()) {
        await imageFile.delete();
      }
    } catch (e) {
      print('Error removing cached image for $gameName: $e');
    }
  }

  // Clear all cached posters
  Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      // Remove all poster cache entries
      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          await prefs.remove(key);
        }
      }
      
      // Remove all cached images
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync();
      
      for (final file in files) {
        if (file.path.contains(_imageCachePrefix) && file.path.endsWith('.jpg')) {
          await file.delete();
        }
      }
    } catch (e) {
      print('Error clearing all cache: $e');
    }
  }

  // Get cache size in bytes
  Future<int> getCacheSize() async {
    int totalSize = 0;
    
    try {
      // Calculate SharedPreferences cache size
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          final value = prefs.getString(key);
          if (value != null) {
            totalSize += value.length;
          }
        }
      }
      
      // Calculate image cache size
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync();
      
      for (final file in files) {
        if (file.path.contains(_imageCachePrefix) && file.path.endsWith('.jpg')) {
          final stat = await File(file.path).stat();
          totalSize += stat.size;
        }
      }
    } catch (e) {
      print('Error calculating cache size: $e');
    }
    
    return totalSize;
  }

  // Helper methods
  String _getCacheKey(String gameName) {
    return '$_cacheKeyPrefix${_sanitizeKey(gameName)}';
  }

  String _getImageCacheKey(String gameName) {
    return '$_imageCachePrefix${_sanitizeKey(gameName)}';
  }

  String _sanitizeKey(String key) {
    // Remove special characters and convert to lowercase
    return key.toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
  }
} 
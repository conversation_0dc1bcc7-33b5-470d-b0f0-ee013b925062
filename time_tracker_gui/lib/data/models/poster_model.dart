class Poster {
  final int id;
  final String name;
  final String backgroundImage;
  final double? rating;
  final String? released;
  final List<String> genres;
  final List<String> platforms;

  Poster({
    required this.id,
    required this.name,
    required this.backgroundImage,
    this.rating,
    this.released,
    this.genres = const [],
    this.platforms = const [],
  });

  // Factory constructor for creating a Poster object from RAWG API JSON response
  factory Poster.fromJson(Map<String, dynamic> json) {
    return Poster(
      id: json['id'] ?? 0,
      name: json['name'] ?? 'Unknown Game',
      backgroundImage: json['background_image'] ?? '',
      rating: json['rating']?.toDouble(),
      released: json['released'],
      genres: (json['genres'] as List<dynamic>?)
          ?.map((genre) => genre['name'] as String)
          .toList() ?? [],
      platforms: (json['platforms'] as List<dynamic>?)
          ?.map((platform) => platform['platform']['name'] as String)
          .toList() ?? [],
    );
  }

  // Method to convert a Poster object to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'background_image': backgroundImage,
      'rating': rating,
      'released': released,
      'genres': genres.map((genre) => {'name': genre}).toList(),
      'platforms': platforms.map((platform) => {'platform': {'name': platform}}).toList(),
    };
  }

  // Helper method to get formatted rating
  String get formattedRating => rating != null ? rating!.toStringAsFixed(1) : 'N/A';

  // Helper method to check if image is available
  bool get hasImage => backgroundImage.isNotEmpty;
} 
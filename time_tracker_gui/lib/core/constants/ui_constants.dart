class UIConstants {
  // Responsive breakpoints with hysteresis to prevent flickering
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  // Hysteresis values to prevent layout switching at exact breakpoints
  static const double breakpointHysteresis = 50;

  // Modern spacing system (4px grid)
  static const double spacingXS = 6.0;
  static const double spacingS = 12.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;
  static const double spacingXXXL = 64.0;

  // Border radius system
  static const double radiusXS = 4.0;
  static const double radiusS = 9.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;

  // Component sizing
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  static const double inputHeight = 48.0;
  static const double cardMinHeight = 80.0;
  static const double appBarHeight = 56.0;

  // Touch targets (accessibility)
  static const double minTouchTarget = 44.0;
  static const double recommendedTouchTarget = 48.0;

  // Icon sizes
  static const double iconXS = 20.0;
  static const double iconS = 25.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;

  // Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 250);
  static const Duration animationSlow = Duration(milliseconds: 400);
  static const Duration animationSlower = Duration(milliseconds: 600);

  // Animation curves (removed - use Curves.* directly)

  // Layout constraints
  static const double maxContentWidth = 1440.0;
  static const double sidebarWidth = 280.0;
  static const double navigationRailWidth = 80.0;

  // Z-index layers
  static const double elevationNone = 0.0;
  static const double elevationLow = 1.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationHighest = 16.0;
}

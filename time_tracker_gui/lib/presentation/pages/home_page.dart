import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';

import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../widgets/shared_hero_components.dart';
import '../widgets/dashboard_components.dart';
import '../widgets/apps_page_components.dart';
import '../widgets/timeline_components.dart';
import '../widgets/statistics_chart.dart';
import '../widgets/common_ui_components.dart';
import '../widgets/dialog_components.dart';
import '../widgets/app_list_widget.dart';
import '../widgets/gaming_app_bar.dart';
import '../widgets/floating_navigation.dart';
import '../providers/app_providers.dart';
import '../providers/poster_providers.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';

// Page definitions with their app bar properties
class AppPage {
  final String title;
  final IconData icon;
  final IconData navIcon;
  final String navLabel;
  final Widget Function() builder;

  const AppPage({
    required this.title,
    required this.icon,
    required this.navIcon,
    required this.navLabel,
    required this.builder,
  });
}

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;
  bool _isCurrentlyDesktop = false;

  late final List<AppPage> _pages = [
    AppPage(
      title: 'Gaming Dashboard',
      icon: Icons.dashboard_rounded,
      navIcon: Icons.dashboard_rounded,
      navLabel: 'Dashboard',
      builder: () => _buildDashboard(),
    ),
    AppPage(
      title: 'Game Library',
      icon: Icons.videogame_asset_rounded,
      navIcon: Icons.videogame_asset_rounded,
      navLabel: 'Games',
      builder: () => _buildAppsPage(),
    ),
    AppPage(
      title: 'Gaming Timeline',
      icon: Icons.timeline_rounded,
      navIcon: Icons.timeline_rounded,
      navLabel: 'Timeline',
      builder: () => _buildTimelinePage(),
    ),
    AppPage(
      title: 'Gaming Analytics',
      icon: Icons.analytics_rounded,
      navIcon: Icons.analytics_rounded,
      navLabel: 'Analytics',
      builder: () => _buildStatisticsPage(),
    ),
  ];

  late final List<NavigationItem> _navigationItems = _pages.map((page) => NavigationItem(
    title: page.title,
    icon: page.icon,
    navIcon: page.navIcon,
    navLabel: page.navLabel,
  )).toList();

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: _pages.length, vsync: this);

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _selectedIndex = _tabController.index;
        });
      }
    });

    // Initialize connection to backend
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(timeTrackerRepositoryProvider).connect();
      // Set initial desktop state
      _isCurrentlyDesktop = _shouldUseDesktopLayout(context);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    ref.read(timeTrackerRepositoryProvider).disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentPage = _pages[_selectedIndex];

    // Use hysteresis to prevent flickering at breakpoint
    final shouldBeDesktop = _shouldUseDesktopLayout(context);
    if (shouldBeDesktop != _isCurrentlyDesktop) {
      // Only update if we're clearly past the hysteresis threshold
      final width = MediaQuery.of(context).size.width;
      if ((shouldBeDesktop && width > UIConstants.tabletBreakpoint + UIConstants.breakpointHysteresis) ||
          (!shouldBeDesktop && width < UIConstants.tabletBreakpoint - UIConstants.breakpointHysteresis)) {
        _isCurrentlyDesktop = shouldBeDesktop;
      }
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: GamingAppBar(
        title: currentPage.title,
        icon: currentPage.icon,
      ),
      body: _isCurrentlyDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
      bottomNavigationBar: _isCurrentlyDesktop ? null : _buildBottomNavigation(),
    );
  }

  Widget _buildDesktopLayout() {
    return _buildPageContent(_selectedIndex);
  }

  Widget _buildMobileLayout() {
    return TabBarView(
      controller: _tabController,
      children: _pages.asMap().entries.map((entry) =>
        _buildPageContent(entry.key)
      ).toList(),
    );
  }

  Widget _buildPageContent(int index) {
    final currentPage = _pages[index];
    return currentPage.builder();
  }

  Widget _buildDashboard() {
    final trackingStatus = ref.watch(trackingStatusProvider);
    final apps = ref.watch(appsProvider);
    final postersAsyncValue = ref.watch(postersProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? theme.darkBg : theme.colorScheme.surface,
      child: Column(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Dashboard Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(DesignTokens.spacingL),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  child: ModernColumn(
                    spacing: DesignTokens.spacingL,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick Stats Grid
                      DashboardComponents.buildEnhancedStatsGrid(trackingStatus, apps, isDark, theme),

                      // Recent Games with Gaming Theme
                      DashboardComponents.buildGamingAppsSection(
                        apps,
                        isDark,
                        theme,
                        () => _tabController.animateTo(1)
                      ),

                      // Featured Posters with Gaming Theme
                      DashboardComponents.buildFeaturedPostersSection(postersAsyncValue, isDark, theme),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppsPage() {
    final filteredApps = ref.watch(filteredAppsProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? theme.darkBg : theme.colorScheme.surface,
      child: Column(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Apps Content
          Expanded(
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
                child: Column(
                  children: [
                    // Enhanced Header Section with Frosted Glass
                    AppsPageComponents.buildGamingAppsHeader(isDark, theme, ref),

                    // Apps List with view toggle
                    Expanded(
                      child: filteredApps.when(
                        data: (appList) {
                          return appList.isEmpty
                              ? AppsPageComponents.buildEmptyGamesLibrary(isDark, theme, ref)
                              : Center(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
                                    child: Padding(
                                      padding: EdgeInsets.all(UIConstants.spacingL.w),
                                      child: AppListWidget(
                                        apps: appList,
                                        showViewToggle: true,
                                      ),
                                    ),
                                  ),
                                );
                        },
                        loading: () => const Center(child: CircularProgressIndicator()),
                        error: (error, _) => CommonUIComponents.buildErrorState('Failed to load gaming library'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelinePage() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ModernContainer(
      child: ModernColumn(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Timeline Content
          Expanded(
            child: ModernColumn(
              children: [
                // Timeline Filter Header
                _buildTimelineHeader(isDark, theme, ref),

                // Timeline List
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(DesignTokens.spacingL),
                    child: Center(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 1200),
                        child: const TimelineComponents(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineHeader(bool isDark, ThemeData theme, WidgetRef ref) {
    final filterState = ref.watch(timelineFilterProvider);
    final hasActiveFilters = filterState.searchQuery.isNotEmpty ||
                           filterState.filterType != TimelineFilterType.all ||
                           filterState.sortBy != TimelineSortBy.dateNewest ||
                           filterState.selectedAppId != null;

    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: ModernColumn(
            spacing: DesignTokens.spacingM,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ModernRow(
                spacing: DesignTokens.spacingM,
                children: [
                  Expanded(
                    child: ModernTextField(
                      hintText: 'Search your gaming timeline...',
                      controller: TextEditingController(text: filterState.searchQuery),
                      onChanged: (value) => ref.read(timelineFilterProvider.notifier).updateSearchQuery(value),
                      prefixIcon: const Icon(Icons.search),
                      variant: InputVariant.filled,
                    ),
                  ),
                  Stack(
                    children: [
                      SecondaryButton(
                        onPressed: () => DialogComponents.showTimelineFilterDialog(context, ref),
                        icon: Icons.filter_list,
                        size: ButtonSize.medium,
                        child: const Text('Filter'),
                      ),
                      if (hasActiveFilters)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: StatusIndicator(
                            status: StatusType.tracking,
                            size: 8,
                          ),
                        ),
                    ],
                  ),
                  if (hasActiveFilters) ...[
                    SizedBox(width: UIConstants.spacingS),
                    CommonUIComponents.buildClearFiltersButton(
                      onPressed: () => ref.read(timelineFilterProvider.notifier).clearFilters(),
                      isDark: isDark,
                      tooltip: 'Clear all filters',
                    ),
                  ],
                ],
              ),
              if (hasActiveFilters) ...[
                SizedBox(height: UIConstants.spacingS),
                Wrap(
                  spacing: UIConstants.spacingS,
                  children: [
                    if (filterState.searchQuery.isNotEmpty)
                      AppsPageComponents.buildFilterChip(
                        'Search: "${filterState.searchQuery}"',
                        () => ref.read(timelineFilterProvider.notifier).updateSearchQuery(''),
                        isDark,
                        theme,
                      ),
                    if (filterState.filterType != TimelineFilterType.all)
                      AppsPageComponents.buildFilterChip(
                        'Period: ${TimelineComponents.getFilterTypeLabel(filterState.filterType)}',
                        () => ref.read(timelineFilterProvider.notifier).updateFilterType(TimelineFilterType.all),
                        isDark,
                        theme,
                      ),
                    if (filterState.sortBy != TimelineSortBy.dateNewest)
                      AppsPageComponents.buildFilterChip(
                        'Sort: ${TimelineComponents.getSortByLabel(filterState.sortBy)}',
                        () => ref.read(timelineFilterProvider.notifier).updateSortBy(TimelineSortBy.dateNewest),
                        isDark,
                        theme,
                      ),
                    if (filterState.selectedAppId != null)
                      AppsPageComponents.buildFilterChip(
                        'Game Filter Active',
                        () => ref.read(timelineFilterProvider.notifier).updateSelectedApp(null),
                        isDark,
                        theme,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsPage() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ModernContainer(
      child: ModernColumn(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Statistics Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacingL),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  child: ModernInfoCard(
                    title: 'Gaming Analytics',
                    icon: Icons.analytics,
                    child: ModernColumn(
                      spacing: DesignTokens.spacingL,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Comprehensive insights into your gaming habits',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.neutral600,
                          ),
                        ),
                        SizedBox(
                          height: 400,
                          child: Consumer(
                            builder: (context, ref, child) {
                              final statistics = ref.watch(statisticsProvider);

                              return statistics.when(
                                data: (stats) => stats.isEmpty
                                  ? _buildEmptyStatisticsState(isDark, theme)
                                  : StatisticsChart(statistics: stats),
                                loading: () => const Center(child: CircularProgressIndicator()),
                                error: (error, _) => _buildErrorStatisticsState(error.toString(), isDark, theme),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStatisticsState(bool isDark, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: UIConstants.iconXL,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: UIConstants.spacingL.h),
          Text(
            'No Gaming Data Yet',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: UIConstants.spacingM.h),
          Text(
            'Start tracking your gaming sessions to see detailed analytics here',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorStatisticsState(String error, bool isDark, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: UIConstants.iconXL,
            color: theme.colorScheme.error,
          ),
          SizedBox(height: UIConstants.spacingL.h),
          Text(
            'Failed to Load Statistics',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: UIConstants.spacingM.h),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget? _buildBottomNavigation() {
    if (_isCurrentlyDesktop) return null;

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.4)
              : theme.colorScheme.surface.withOpacity(0.9),
            border: Border(
              top: BorderSide(
                color: isDark
                  ? Colors.white.withOpacity(0.1)
                  : theme.colorScheme.outline.withOpacity(0.3),
                width: 1,
              ),
            ),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
              _tabController.animateTo(index);
            },
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: theme.steamAccent,
            unselectedItemColor: isDark
              ? Colors.white.withOpacity(0.6)
              : theme.colorScheme.onSurfaceVariant,
            items: _pages.map((page) => BottomNavigationBarItem(
              icon: Icon(page.navIcon),
              label: page.navLabel,
            )).toList(),
          ),
        ),
      ),
    );
  }

  bool _shouldUseDesktopLayout(BuildContext context) {
    return MediaQuery.of(context).size.width > UIConstants.tabletBreakpoint;
  }

  bool _isDesktop(BuildContext context) {
    return _isCurrentlyDesktop;
  }
}

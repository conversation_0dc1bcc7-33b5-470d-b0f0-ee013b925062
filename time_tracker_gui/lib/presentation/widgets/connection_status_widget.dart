import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/app_providers.dart';
import '../../app/theme/app_theme.dart';
import '../../core/constants/ui_constants.dart';

class ConnectionStatusWidget extends ConsumerWidget {
  final bool isCompact;
  final bool showLastUpdate;

  const ConnectionStatusWidget({
    super.key,
    this.isCompact = false,
    this.showLastUpdate = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStatus = ref.watch(connectionStatusProvider);
    final trackingStatus = ref.watch(trackingStatusProvider);

    return _buildStatusIndicator(
      context: context,
      connectionStatus: connectionStatus,
      trackingStatus: trackingStatus,
    );
  }

  Widget _buildStatusIndicator({
    required BuildContext context,
    required ConnectionStatus connectionStatus,
    required AsyncValue trackingStatus,
  }) {
    IconData icon;
    String label;
    Color color;
    String tooltip;
    bool pulseAnimation = false;

    if (!connectionStatus.isConnected) {
      icon = Icons.wifi_off;
      label = isCompact ? 'OFF' : 'Offline';
      color = AppTheme.errorColor;
      tooltip = connectionStatus.errorMessage != null
          ? 'Disconnected: ${connectionStatus.errorMessage}'
          : 'Disconnected from server';
    } else {
      switch (connectionStatus.connectionType) {
        case ConnectionType.websocket:
          icon = Icons.wifi;
          label = isCompact ? 'LIVE' : 'Real-time';
          color = AppTheme.successColor;
          tooltip = 'Connected via WebSocket - Real-time updates active';
          pulseAnimation = true;
          break;
        case ConnectionType.http:
          icon = Icons.sync;
          label = isCompact ? 'SYNC' : 'Syncing';
          color = AppTheme.warningColor;
          tooltip = 'Connected via HTTP polling - Updates every second';
          pulseAnimation = true;
          break;
        case ConnectionType.disconnected:
          icon = Icons.wifi_off;
          label = isCompact ? 'OFF' : 'Offline';
          color = AppTheme.errorColor;
          tooltip = 'Disconnected from server';
          break;
      }
    }

    Widget indicator = Container(
      margin: EdgeInsets.only(right: UIConstants.spacingS.w),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(UIConstants.radiusS),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 10),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isCompact ? 6 : 8,
              vertical: isCompact ? 4 : 6,
            ),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(UIConstants.radiusS),
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                pulseAnimation
                    ? _PulsingIcon(
                        icon: icon,
                        color: color,
                        size: isCompact ? UIConstants.iconXS : UIConstants.iconS
                      )
                    : Icon(
                        icon,
                        size: isCompact ? UIConstants.iconXS : UIConstants.iconS,
                        color: color,
                      ),
                if (!isCompact) ...[
                  SizedBox(width: UIConstants.spacingXS),
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                      fontSize: 11,
                    ),
                  ),
                ],
                if (showLastUpdate && trackingStatus != null)
                  _buildDataQualityIndicator(context, trackingStatus, color),
              ],
            ),
          ),
        ),
      ),
    );

    return Tooltip(
      message: tooltip,
      child: indicator,
    );
  }

  Widget _buildDataQualityIndicator(BuildContext context, AsyncValue trackingStatus, Color baseColor) {
    return Padding(
      padding: EdgeInsets.only(left: UIConstants.spacingXS),
      child: trackingStatus.when(
        data: (status) => Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: AppTheme.successColor,
            shape: BoxShape.circle,
          ),
        ),
        loading: () => SizedBox(
          width: 6,
          height: 6,
          child: CircularProgressIndicator(
            strokeWidth: 1.5,
            valueColor: AlwaysStoppedAnimation<Color>(baseColor),
          ),
        ),
        error: (_, __) => Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: AppTheme.errorColor,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}

class _PulsingIcon extends StatefulWidget {
  final IconData icon;
  final Color color;
  final double size;

  const _PulsingIcon({
    required this.icon,
    required this.color,
    required this.size,
  });

  @override
  State<_PulsingIcon> createState() => _PulsingIconState();
}

class _PulsingIconState extends State<_PulsingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Icon(
            widget.icon,
            size: widget.size,
            color: widget.color,
          ),
        );
      },
    );
  }
}

class ConnectionStatusBanner extends ConsumerWidget {
  const ConnectionStatusBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStatus = ref.watch(connectionStatusProvider);

    if (connectionStatus.isConnected &&
        connectionStatus.connectionType == ConnectionType.websocket) {
      return const SizedBox.shrink();
    }

    Color backgroundColor;
    String message;
    IconData icon;
    Widget? action;

    if (!connectionStatus.isConnected) {
      backgroundColor = AppTheme.errorColor;
      message = connectionStatus.errorMessage != null
          ? 'Connection failed: ${connectionStatus.errorMessage}'
          : 'Disconnected from server. Attempting to reconnect...';
      icon = Icons.wifi_off;
      action = SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(backgroundColor),
        ),
      );
    } else {
      backgroundColor = AppTheme.warningColor;
      message = 'Using HTTP sync mode. Data updates every second.';
      icon = Icons.sync;
      action = Icon(
        Icons.check_circle_outline,
        size: 16,
        color: backgroundColor,
      );
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM,
        vertical: UIConstants.spacingS,
      ),
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: backgroundColor.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: UIConstants.iconS,
            color: backgroundColor,
          ),
          SizedBox(width: UIConstants.spacingS),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: backgroundColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (action != null) action,
        ],
      ),
    );
  }
}

class ConnectionStatusAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;

  const ConnectionStatusAppBar({
    super.key,
    required this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      title: Text(title),
      actions: [
        const ConnectionStatusWidget(isCompact: true, showLastUpdate: true),
        SizedBox(width: UIConstants.spacingS),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

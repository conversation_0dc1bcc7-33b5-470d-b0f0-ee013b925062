import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:io';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../core/constants/ui_constants.dart';
import '../providers/local_game_poster_providers.dart';

class LocalGamePosterWidget extends ConsumerWidget {
  final AppModel app;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const LocalGamePosterWidget({
    super.key,
    required this.app,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final gameName = _getGameName(app);

    return Container(
      width: width ?? 200.w,
      height: height ?? 280.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Game poster or placeholder
            if (gameName.isNotEmpty)
              _buildPosterImage(ref, gameName, isDark)
            else
              _buildGamePlaceholder(isDark),

            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                  stops: const [0.6, 1.0],
                ),
              ),
            ),

            // Game info overlay
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.all(UIConstants.spacingM.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Game name
                    Text(
                      _getDisplayName(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.8),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: UIConstants.spacingXS.h),

                    // Game stats
                    Row(
                      children: [
                        // Play time
                        if (app.duration != null) ...[
                          Icon(
                            Icons.access_time,
                            size: 14.sp,
                            color: Colors.white70,
                          ),
                          SizedBox(width: UIConstants.spacingXS.w),
                          Text(
                            _formatDuration(app.duration!),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],

                        const Spacer(),

                        // Launch count
                        if (app.launches != null) ...[
                          Icon(
                            Icons.play_arrow,
                            size: 14.sp,
                            color: Colors.white70,
                          ),
                          SizedBox(width: UIConstants.spacingXS.w),
                          Text(
                            '${app.launches}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Tap handler
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(UIConstants.radiusL),
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPosterImage(WidgetRef ref, String gameName, bool isDark) {
    final posterAsyncValue = ref.watch(cachedPosterProvider(gameName));
    final imagePathAsyncValue = ref.watch(cachedImagePathProvider(gameName));

    return posterAsyncValue.when(
      data: (poster) {
        if (poster != null && poster.hasImage) {
          return imagePathAsyncValue.when(
            data: (imagePath) {
              if (imagePath != null) {
                // Use cached local image
                return Image.file(
                  File(imagePath),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to network image if local file fails
                    return _buildNetworkImage(poster.backgroundImage, isDark);
                  },
                );
              } else {
                // Use network image
                return _buildNetworkImage(poster.backgroundImage, isDark);
              }
            },
            loading: () => _buildNetworkImage(poster.backgroundImage, isDark),
            error: (error, stackTrace) => _buildNetworkImage(poster.backgroundImage, isDark),
          );
        } else {
          return _buildGamePlaceholder(isDark);
        }
      },
      loading: () => _buildLoadingPlaceholder(isDark),
      error: (error, stackTrace) => _buildGamePlaceholder(isDark),
    );
  }

  Widget _buildNetworkImage(String imageUrl, bool isDark) {
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _buildLoadingPlaceholder(isDark);
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildGamePlaceholder(isDark);
      },
    );
  }

  Widget _buildLoadingPlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [Colors.grey[800]!, Colors.grey[900]!]
              : [Colors.grey[300]!, Colors.grey[400]!],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 24.sp,
            height: 24.sp,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDark ? Colors.grey[600]! : Colors.grey[500]!,
              ),
            ),
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            'Loading...',
            style: TextStyle(
              color: isDark ? Colors.grey[600] : Colors.grey[500],
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGamePlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [Colors.grey[800]!, Colors.grey[900]!]
              : [Colors.grey[300]!, Colors.grey[400]!],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.videogame_asset,
            size: 48.sp,
            color: isDark ? Colors.grey[600] : Colors.grey[500],
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            'Local Game',
            style: TextStyle(
              color: isDark ? Colors.grey[600] : Colors.grey[500],
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _getDisplayName() {
    if (app.productName?.isNotEmpty == true) {
      return app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      return app.name!;
    } else {
      return 'Unknown Game';
    }
  }

  String _getGameName(AppModel app) {
    String gameName = '';
    
    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }
    
    // Clean up the game name for better search results
    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return gameName;
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;

    if (hours > 0) {
      return '${hours}h ${mins}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${mins}m';
    }
  }
} 
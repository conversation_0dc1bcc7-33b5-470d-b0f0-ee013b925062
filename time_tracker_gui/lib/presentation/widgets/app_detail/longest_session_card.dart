import 'package:flutter/material.dart';

import '../../../data/models/app_model.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';
class LongestSessionCard extends StatelessWidget {
  final AppModel app;

  const LongestSessionCard({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context) {
    final longestSession = app.longestSession ?? 0;
    final formattedDuration = TimeUtil.formatDuration(longestSession);

    return ModernInfoCard(
      title: 'Longest Session',
      icon: Icons.timer,
      child: ModernColumn(
        spacing: DesignTokens.spacingS,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            formattedDuration,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: AppColors.steamBlue,
            ),
          ),
          Text(
            'Your longest gaming session for this app',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.neutral600,
            ),
          ),
        ],
      ),
    );
  }


}
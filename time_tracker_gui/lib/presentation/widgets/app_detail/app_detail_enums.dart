import 'package:flutter/material.dart';

enum TimeRange {
  today('Today'),
  lastSevenDays('Last 7 days'),
  lastThirtyDays('Last 30 days'),
  allTime('All time');

  const TimeRange(this.displayName);
  final String displayName;

  int get daysCount => switch (this) {
    TimeRange.today => 1,
    TimeRange.lastSevenDays => 7,
    TimeRange.lastThirtyDays => 30,
    TimeRange.allTime => 0, // Special case for all time
  };

  DateTimeRange? getDateRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return switch (this) {
      TimeRange.today => DateTimeRange(
        start: today,
        end: today.add(const Duration(days: 1)),
      ),
      TimeRange.lastSevenDays => DateTimeRange(
        start: today.subtract(const Duration(days: 7)),
        end: today.add(const Duration(days: 1)),
      ),
      TimeRange.lastThirtyDays => DateTimeRange(
        start: today.subtract(const Duration(days: 30)),
        end: today.add(const Duration(days: 1)),
      ),
      TimeRange.allTime => null, // No filtering
    };
  }
}

enum SessionSortOption {
  durationLongest('Duration (Longest)'),
  durationShortest('Duration (Shortest)'),
  dateNewest('Date (Newest)'),
  dateOldest('Date (Oldest)');

  const SessionSortOption(this.displayName);
  final String displayName;
}

class UsageStatistics {
  final int totalSessions;
  final int totalDuration;
  final int averageSession;
  final int longestSession;
  final int dailyAverage;

  const UsageStatistics({
    required this.totalSessions,
    required this.totalDuration,
    required this.averageSession,
    required this.longestSession,
    required this.dailyAverage,
  });

  static const empty = UsageStatistics(
    totalSessions: 0,
    totalDuration: 0,
    averageSession: 0,
    longestSession: 0,
    dailyAverage: 0,
  );
}
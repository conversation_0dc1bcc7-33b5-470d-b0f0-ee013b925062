import '../../../data/models/app_model.dart';
import 'app_detail_enums.dart';

class SessionService {
  static List<TimelineModel> filterSessionsByTimeRange(
    List<TimelineModel> sessions,
    TimeRange timeRange,
  ) {
    final dateRange = timeRange.getDateRange();
    if (dateRange == null) return sessions;

    return sessions.where((session) {
      final sessionDate = session.date;
      if (sessionDate == null) return false;

      final normalizedDate = DateTime(
        sessionDate.year,
        sessionDate.month,
        sessionDate.day,
      );

      return normalizedDate.isAfter(
        dateRange.start.subtract(const Duration(days: 1))
      ) && normalizedDate.isBefore(dateRange.end);
    }).toList();
  }

  static List<TimelineModel> filterSessionsByCheckpoint(
    List<TimelineModel> sessions,
    CheckpointModel? checkpoint,
  ) {
    if (checkpoint == null) return sessions;

    return sessions.where((session) {
      return session.checkpointId == checkpoint.id ||
             session.checkpointAssociations.contains(checkpoint.id);
    }).toList();
  }

  static List<TimelineModel> filterSessions(
    List<TimelineModel> sessions,
    TimeRange timeRange,
    CheckpointModel? checkpoint,
  ) {
    var filteredSessions = filterSessionsByTimeRange(sessions, timeRange);
    filteredSessions = filterSessionsByCheckpoint(filteredSessions, checkpoint);
    return filteredSessions;
  }

  static List<TimelineModel> sortSessions(
    List<TimelineModel> sessions,
    SessionSortOption sortOption,
  ) {
    final sortedSessions = List<TimelineModel>.from(sessions);

    return switch (sortOption) {
      SessionSortOption.durationLongest => sortedSessions
        ..sort((a, b) => (b.duration ?? 0).compareTo(a.duration ?? 0)),
      SessionSortOption.durationShortest => sortedSessions
        ..sort((a, b) => (a.duration ?? 0).compareTo(b.duration ?? 0)),
      SessionSortOption.dateNewest => sortedSessions
        ..sort(_compareDateNewest),
      SessionSortOption.dateOldest => sortedSessions
        ..sort(_compareDateOldest),
    };
  }

  static int _compareDateNewest(TimelineModel a, TimelineModel b) {
    return switch ((a.date, b.date)) {
      (null, null) => 0,
      (null, _) => 1,
      (_, null) => -1,
      (final DateTime dateA, final DateTime dateB) => dateB.compareTo(dateA),
    };
  }

  static int _compareDateOldest(TimelineModel a, TimelineModel b) {
    return switch ((a.date, b.date)) {
      (null, null) => 0,
      (null, _) => 1,
      (_, null) => -1,
      (final DateTime dateA, final DateTime dateB) => dateA.compareTo(dateB),
    };
  }

  static UsageStatistics calculateUsageStats(
    List<TimelineModel> sessions,
    TimeRange timeRange, {
    CheckpointModel? checkpoint,
  }) {
    final filteredSessions = filterSessions(sessions, timeRange, checkpoint);

    if (filteredSessions.isEmpty) return UsageStatistics.empty;

    final totalDuration = filteredSessions.fold<int>(
      0, (sum, session) => sum + (session.duration ?? 0),
    );

    final longestSession = filteredSessions.fold<int>(
      0, (max, session) => switch (session.duration) {
        final duration? when duration > max => duration,
        _ => max,
      },
    );

    final averageSession = filteredSessions.isNotEmpty
        ? totalDuration ~/ filteredSessions.length
        : 0;

    final days = _calculateDaysForTimeRange(timeRange, filteredSessions);
    final dailyAverage = days > 0 ? totalDuration ~/ days : 0;

    return UsageStatistics(
      totalSessions: filteredSessions.length,
      totalDuration: totalDuration,
      averageSession: averageSession,
      longestSession: longestSession,
      dailyAverage: dailyAverage,
    );
  }

  static int _calculateDaysForTimeRange(
    TimeRange timeRange,
    List<TimelineModel> sessions,
  ) {
    if (timeRange == TimeRange.allTime) {
      final dateRange = _getDateRangeFromSessions(sessions);
      final days = dateRange?.inDays ?? 0;
      return days > 0 ? days : 1;
    } else {
      return timeRange.daysCount > 0 ? timeRange.daysCount : 1;
    }
  }

  static Duration? _getDateRangeFromSessions(List<TimelineModel> sessions) {
    final dates = sessions
        .where((s) => s.date != null)
        .map((s) => s.date!)
        .toList();

    if (dates.isEmpty) return null;

    dates.sort();
    return dates.last.difference(dates.first);
  }

  static String getFilterDescription(TimeRange timeRange, CheckpointModel? checkpoint) {
    final timeDesc = timeRange.displayName;
    if (checkpoint != null) {
      return '$timeDesc • ${checkpoint.name}';
    }
    return timeDesc;
  }
}
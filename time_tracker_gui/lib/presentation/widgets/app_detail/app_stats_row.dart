import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/app_providers.dart';
import '../../../app/design_system/design_system.dart';

class AppStatsRow extends ConsumerWidget {
  final AppModel app;

  const AppStatsRow({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ModernRow(
      spacing: DesignTokens.spacingM,
      children: [
        Expanded(
          child: ModernStatsCard(
            title: 'Launches',
            value: '${app.launches ?? 0}',
            icon: Icons.launch,
            accentColor: AppColors.steamBlue,
          ),
        ),
        Expanded(
          child: ModernStatsCard(
            title: 'Avg Session',
            value: _formatDuration(_calculateAverageSession()),
            icon: Icons.timer,
            accentColor: AppColors.success,
          ),
        ),
        Expanded(
          child: ModernStatsCard(
            title: 'Last Played',
            value: _formatLastPlayed(ref),
            icon: Icons.schedule,
            accentColor: AppColors.warning,
          ),
        ),
      ],
    );
  }



  int _calculateAverageSession() {
    final totalDuration = app.duration ?? 0;
    final launches = app.launches ?? 1;
    return launches > 0 ? totalDuration ~/ launches : 0;
  }

  String _formatLastPlayed(WidgetRef ref) {
    // Get all timeline data for this app to find the most recent session
    final allTimeTimeline = ref.watch(allTimeTimelineProvider);

    return allTimeTimeline.when(
      data: (timeline) {
        // Filter timeline for this specific app and find the most recent session
        final appSessions = timeline
            .where((session) => session.appId == app.id && session.date != null)
            .toList();

        if (appSessions.isEmpty) return 'Never';

        // Sort by date to get the most recent session
        appSessions.sort((a, b) => b.date!.compareTo(a.date!));
        final mostRecentSession = appSessions.first;

        final now = DateTime.now();
        final difference = now.difference(mostRecentSession.date!).inDays;

        return switch (difference) {
          0 => 'Today',
          1 => 'Yesterday',
          < 7 => '${difference}d ago',
          < 30 => '${difference ~/ 7}w ago',
          < 365 => '${difference ~/ 30}mo ago',
          _ => _formatActualDate(mostRecentSession.date!),
        };
      },
      loading: () => '...',
      error: (_, __) => 'Never',
    );
  }

  String _formatActualDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatDuration(int minutes) {
    return minutes < 60
      ? '${minutes}m'
      : '${minutes ~/ 60}h ${minutes % 60}m';
  }
}
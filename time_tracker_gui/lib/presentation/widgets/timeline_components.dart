import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import 'common_ui_components.dart';

/// Helper class to distinguish between null and undefined values in copyWith
class _Undefined {
  const _Undefined();
}

/// Timeline filter types for different time periods
enum TimelineFilterType {
  all,
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  custom,
}

/// Timeline sorting options
enum TimelineSortBy {
  dateNewest,
  dateOldest,
  durationLongest,
  durationShortest,
  appName,
}

/// State class for timeline filtering and sorting
class TimelineFilterState {
  final TimelineFilterType filterType;
  final TimelineSortBy sortBy;
  final String searchQuery;
  final DateTime? customStartDate;
  final DateTime? customEndDate;
  final int? selectedAppId;
  final bool showAll;

  const TimelineFilterState({
    this.filterType = TimelineFilterType.all,
    this.sortBy = TimelineSortBy.dateNewest,
    this.searchQuery = '',
    this.customStartDate,
    this.customEndDate,
    this.selectedAppId,
    this.showAll = false,
  });

  TimelineFilterState copyWith({
    TimelineFilterType? filterType,
    TimelineSortBy? sortBy,
    String? searchQuery,
    Object? customStartDate = const _Undefined(),
    Object? customEndDate = const _Undefined(),
    Object? selectedAppId = const _Undefined(),
    bool? showAll,
  }) {
    return TimelineFilterState(
      filterType: filterType ?? this.filterType,
      sortBy: sortBy ?? this.sortBy,
      searchQuery: searchQuery ?? this.searchQuery,
      customStartDate: customStartDate is _Undefined ? this.customStartDate : customStartDate as DateTime?,
      customEndDate: customEndDate is _Undefined ? this.customEndDate : customEndDate as DateTime?,
      selectedAppId: selectedAppId is _Undefined ? this.selectedAppId : selectedAppId as int?,
      showAll: showAll ?? this.showAll,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'filterType': filterType.index,
      'sortBy': sortBy.index,
      'searchQuery': searchQuery,
      'customStartDate': customStartDate?.toIso8601String(),
      'customEndDate': customEndDate?.toIso8601String(),
      'selectedAppId': selectedAppId,
      'showAll': showAll,
    };
  }

  factory TimelineFilterState.fromJson(Map<String, dynamic> json) {
    return TimelineFilterState(
      filterType: TimelineFilterType.values[json['filterType'] ?? 0],
      sortBy: TimelineSortBy.values[json['sortBy'] ?? 0],
      searchQuery: json['searchQuery'] ?? '',
      customStartDate: json['customStartDate'] != null
          ? DateTime.parse(json['customStartDate'])
          : null,
      customEndDate: json['customEndDate'] != null
          ? DateTime.parse(json['customEndDate'])
          : null,
      selectedAppId: json['selectedAppId'],
      showAll: json['showAll'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimelineFilterState &&
        other.filterType == filterType &&
        other.sortBy == sortBy &&
        other.searchQuery == searchQuery &&
        other.customStartDate == customStartDate &&
        other.customEndDate == customEndDate &&
        other.selectedAppId == selectedAppId &&
        other.showAll == showAll;
  }

  @override
  int get hashCode {
    return Object.hash(
      filterType,
      sortBy,
      searchQuery,
      customStartDate,
      customEndDate,
      selectedAppId,
      showAll,
    );
  }
}

/// Provider for timeline filter state management
final timelineFilterProvider = StateNotifierProvider<TimelineFilterNotifier, TimelineFilterState>((ref) {
  return TimelineFilterNotifier();
});

/// Notifier for managing timeline filter state
class TimelineFilterNotifier extends StateNotifier<TimelineFilterState> {
  static const String _timelineFilterKey = 'timeline_filter_state';
  Timer? _debounceTimer;
  Timer? _saveTimer;

  TimelineFilterNotifier() : super(const TimelineFilterState()) {
    _loadFilterState();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadFilterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filterJson = prefs.getString(_timelineFilterKey);

      if (filterJson != null) {
        final Map<String, dynamic> json = jsonDecode(filterJson);
        state = TimelineFilterState.fromJson(json);
      }
    } catch (e) {
      // If loading fails, keep default state
      print('Failed to load timeline filter state: $e');
    }
  }

  Future<void> _saveFilterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_timelineFilterKey, jsonString);
    } catch (e) {
      print('Failed to save timeline filter state: $e');
    }
  }

  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), () {
      _saveFilterState();
    });
  }

  void updateFilterType(TimelineFilterType filterType) {
    state = state.copyWith(filterType: filterType);
    _debouncedSave();
  }

  void updateSortBy(TimelineSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
    _debouncedSave();
  }

  void updateSearchQuery(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // For immediate clearing, don't debounce
    if (query.isEmpty) {
      state = state.copyWith(searchQuery: query);
      _debouncedSave();
      return;
    }

    // Debounce search input to prevent excessive filtering
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      state = state.copyWith(searchQuery: query);
      _debouncedSave();
    });
  }

  void updateCustomDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
      customStartDate: startDate,
      customEndDate: endDate,
    );
    _debouncedSave();
  }

  void updateSelectedApp(int? appId) {
    state = state.copyWith(selectedAppId: appId);
    _debouncedSave();
  }

  void toggleShowAll() {
    state = state.copyWith(showAll: !state.showAll);
    _debouncedSave();
  }

  void clearFilters() {
    _debounceTimer?.cancel();
    _saveTimer?.cancel();
    state = const TimelineFilterState();
    _saveFilterState();
  }
}

/// Optimized provider for app lookup map to avoid O(n²) complexity
final appLookupMapProvider = Provider<AsyncValue<Map<int, AppModel>>>((ref) {
  final apps = ref.watch(appsProvider);
  return apps.when(
    data: (appList) {
      final Map<int, AppModel> appMap = {};
      for (final app in appList) {
        appMap[app.id] = app;
      }
      return AsyncValue.data(appMap);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Optimized provider for filtered and sorted timeline data
final filteredTimelineProvider = Provider<AsyncValue<List<TimelineModel>>>((ref) {
  final timeline = ref.watch(timelineProvider);
  final appMap = ref.watch(appLookupMapProvider);
  final filterState = ref.watch(timelineFilterProvider);

  return timeline.when(
    data: (timelineData) => appMap.when(
      data: (appLookupMap) {
        final filteredData = _filterAndSortTimelineOptimized(timelineData, appLookupMap, filterState);
        return AsyncValue.data(filteredData);
      },
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Optimized filtering and sorting function with O(n) complexity
List<TimelineModel> _filterAndSortTimelineOptimized(
  List<TimelineModel> timeline,
  Map<int, AppModel> appMap,
  TimelineFilterState filterState,
) {
  var filteredTimeline = <TimelineModel>[];

  // Pre-compute search query for efficiency
  final searchQuery = filterState.searchQuery.toLowerCase();
  final hasSearchQuery = searchQuery.isNotEmpty;

  // Pre-compute date ranges for time filtering
  final now = DateTime.now();
  DateTime? filterStartDate;
  DateTime? filterEndDate;

  switch (filterState.filterType) {
    case TimelineFilterType.today:
      filterStartDate = DateTime(now.year, now.month, now.day);
      break;
    case TimelineFilterType.yesterday:
      final yesterday = DateTime(now.year, now.month, now.day - 1);
      filterStartDate = yesterday;
      filterEndDate = DateTime(now.year, now.month, now.day);
      break;
    case TimelineFilterType.thisWeek:
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      filterStartDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
      break;
    case TimelineFilterType.lastWeek:
      final lastWeekEnd = now.subtract(Duration(days: now.weekday));
      final lastWeekStart = lastWeekEnd.subtract(const Duration(days: 6));
      filterStartDate = DateTime(lastWeekStart.year, lastWeekStart.month, lastWeekStart.day);
      filterEndDate = DateTime(lastWeekEnd.year, lastWeekEnd.month, lastWeekEnd.day).add(const Duration(days: 1));
      break;
    case TimelineFilterType.thisMonth:
      filterStartDate = DateTime(now.year, now.month, 1);
      break;
    case TimelineFilterType.lastMonth:
      filterStartDate = DateTime(now.year, now.month - 1, 1);
      filterEndDate = DateTime(now.year, now.month, 1);
      break;
    case TimelineFilterType.custom:
      filterStartDate = filterState.customStartDate;
      filterEndDate = filterState.customEndDate?.add(const Duration(days: 1));
      break;
    case TimelineFilterType.all:
      // No date filtering
      break;
  }

  // Single pass filtering with O(n) complexity
  for (final session in timeline) {
    // Filter by app selection
    if (filterState.selectedAppId != null && session.appId != filterState.selectedAppId) {
      continue;
    }

    // Filter by search query
    if (hasSearchQuery) {
      final app = appMap[session.appId];
      final appName = (app?.name ?? 'Unknown Game').toLowerCase();
      if (!appName.contains(searchQuery)) {
        continue;
      }
    }

    // Filter by date range
    if (filterStartDate != null || filterEndDate != null) {
      if (session.date == null) continue;

      if (filterStartDate != null && session.date!.isBefore(filterStartDate)) {
        continue;
      }

      if (filterEndDate != null && session.date!.isAfter(filterEndDate)) {
        continue;
      }
    }

    filteredTimeline.add(session);
  }

  // Optimized sorting
  switch (filterState.sortBy) {
    case TimelineSortBy.dateNewest:
      filteredTimeline.sort((a, b) {
        if (a.date == null && b.date == null) return 0;
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return b.date!.compareTo(a.date!);
      });
      break;
    case TimelineSortBy.dateOldest:
      filteredTimeline.sort((a, b) {
        if (a.date == null && b.date == null) return 0;
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return a.date!.compareTo(b.date!);
      });
      break;
    case TimelineSortBy.durationLongest:
      filteredTimeline.sort((a, b) => (b.duration ?? 0).compareTo(a.duration ?? 0));
      break;
    case TimelineSortBy.durationShortest:
      filteredTimeline.sort((a, b) => (a.duration ?? 0).compareTo(b.duration ?? 0));
      break;
    case TimelineSortBy.appName:
      filteredTimeline.sort((a, b) {
        final appA = appMap[a.appId];
        final appB = appMap[b.appId];
        final nameA = appA?.name ?? 'Unknown Game';
        final nameB = appB?.name ?? 'Unknown Game';
        return nameA.compareTo(nameB);
      });
      break;
  }

  return filteredTimeline;
}

class TimelineComponents extends ConsumerWidget {
  const TimelineComponents({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filteredTimeline = ref.watch(filteredTimelineProvider);
    final appMap = ref.watch(appLookupMapProvider);
    final filterState = ref.watch(timelineFilterProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return filteredTimeline.when(
      data: (timelineData) => appMap.when(
        data: (appLookupMap) {
          return _buildTimelineList(timelineData, appLookupMap, isDark, theme, ref, filterState);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => CommonUIComponents.buildErrorState('Failed to load apps'),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => CommonUIComponents.buildErrorState('Failed to load timeline'),
    );
  }

  Widget _buildTimelineList(
    List<TimelineModel> timelineData,
    Map<int, AppModel> appMap,
    bool isDark,
    ThemeData theme,
    WidgetRef ref,
    TimelineFilterState filterState,
  ) {
    if (timelineData.isEmpty) {
      return CommonUIComponents.buildGamingEmptyState(
        'No gaming sessions found',
        'Try adjusting your filters or start playing to see your timeline here',
        isDark,
        theme,
      );
    }

    const int initialItemCount = 10;
    final int itemCount = filterState.showAll ? timelineData.length :
        (timelineData.length > initialItemCount ? initialItemCount : timelineData.length);
    final bool hasMoreItems = timelineData.length > initialItemCount && !filterState.showAll;

    return ListView(
      padding: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
      children: [
        ...List.generate(itemCount, (index) {
          final session = timelineData[index];
          final app = appMap[session.appId] ?? AppModel(id: session.appId, name: 'Unknown Game');
          return _buildTimelineItem(session, app, isDark, theme);
        }),
        if (hasMoreItems) ...[
          SizedBox(height: UIConstants.spacingM),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
            child: ElevatedButton.icon(
              onPressed: () => ref.read(timelineFilterProvider.notifier).toggleShowAll(),
              style: ElevatedButton.styleFrom(
                backgroundColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: UIConstants.spacingL),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusL),
                  side: BorderSide(
                    color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    width: 1,
                  ),
                ),
              ),
              icon: Icon(Icons.expand_more),
              label: Text(
                'Show ${timelineData.length - initialItemCount} more sessions',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
        if (filterState.showAll && timelineData.length > initialItemCount) ...[
          SizedBox(height: UIConstants.spacingM),
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
            child: ElevatedButton.icon(
              onPressed: () => ref.read(timelineFilterProvider.notifier).toggleShowAll(),
              style: ElevatedButton.styleFrom(
                backgroundColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: UIConstants.spacingL),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusL),
                  side: BorderSide(
                    color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    width: 1,
                  ),
                ),
              ),
              icon: Icon(Icons.expand_less),
              label: Text(
                'Show less',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimelineItem(TimelineModel session, AppModel app, bool isDark, ThemeData theme) {
    return Container(
      margin: EdgeInsets.only(bottom: UIConstants.spacingXS, top: UIConstants.spacingXS),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withOpacity(0.4)
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingL.w),
        child: Row(
          children: [
            Container(
              width: 6.w,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                    (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.5),
                  ],
                ),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            SizedBox(width: UIConstants.spacingL.w),
            Container(
              padding: EdgeInsets.all(UIConstants.spacingM.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDark ? [
                    AppTheme.infoColor.withOpacity(0.3),
                    AppTheme.infoColor.withOpacity(0.1),
                  ] : [
                    AppTheme.infoColor.withOpacity(0.2),
                    AppTheme.infoColor.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(UIConstants.radiusM),
              ),
              child: Icon(
                Icons.games,
                color: AppTheme.infoColor,
                size: UIConstants.iconM,
              ),
            ),
            SizedBox(width: UIConstants.spacingL.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    app.name ?? 'Unknown Game',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingXS.h),
                  Text(
                    _formatDate(session.date),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: UIConstants.spacingM,
                vertical: UIConstants.spacingM,
              ),
              child: Text(
                _formatDuration(session.duration ?? 0),
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static String getFilterTypeLabel(TimelineFilterType type) {
    switch (type) {
      case TimelineFilterType.all:
        return 'All Time';
      case TimelineFilterType.today:
        return 'Today';
      case TimelineFilterType.yesterday:
        return 'Yesterday';
      case TimelineFilterType.thisWeek:
        return 'This Week';
      case TimelineFilterType.lastWeek:
        return 'Last Week';
      case TimelineFilterType.thisMonth:
        return 'This Month';
      case TimelineFilterType.lastMonth:
        return 'Last Month';
      case TimelineFilterType.custom:
        return 'Custom Range';
    }
  }

  static String getSortByLabel(TimelineSortBy sortBy) {
    switch (sortBy) {
      case TimelineSortBy.dateNewest:
        return 'Newest First';
      case TimelineSortBy.dateOldest:
        return 'Oldest First';
      case TimelineSortBy.durationLongest:
        return 'Longest Sessions';
      case TimelineSortBy.durationShortest:
        return 'Shortest Sessions';
      case TimelineSortBy.appName:
        return 'Game Name';
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown date';

    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today at ${_formatTime(date)}';
    } else if (difference == 1) {
      return 'Yesterday at ${_formatTime(date)}';
    } else if (difference < 7) {
      return '${difference}d ago at ${_formatTime(date)}';
    } else {
      return '${date.day}/${date.month}/${date.year} at ${_formatTime(date)}';
    }
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;

    if (hours > 0) {
      return '${hours}h ${mins}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${mins}m';
    }
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../data/models/app_model.dart';
import '../../core/constants/ui_constants.dart';

class Statistics<PERSON>hart extends StatefulWidget {
  final List<AppStatistics> statistics;

  const StatisticsChart({
    super.key,
    required this.statistics,
  });

  @override
  State<StatisticsChart> createState() => _StatisticsChartState();
}

class _StatisticsChartState extends State<StatisticsChart> {
  int _selectedChartType = 0; // 0: Bar Chart, 1: Pie Chart, 2: Line Chart

  @override
  Widget build(BuildContext context) {
    if (widget.statistics.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics,
              size: 64.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'No statistics available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
        child: Column(
          children: [
            _buildChartTypeSelector(),
            SizedBox(height: UIConstants.spacingS.h),
            Expanded(
              child: _buildSelectedChart(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SegmentedButton<int>(
          segments: const [
            ButtonSegment(
              value: 0,
              label: Text('Bar'),
              icon: Icon(Icons.bar_chart),
            ),
            ButtonSegment(
              value: 1,
              label: Text('Pie'),
              icon: Icon(Icons.pie_chart),
            ),
            ButtonSegment(
              value: 2,
              label: Text('Line'),
              icon: Icon(Icons.show_chart),
            ),
          ],
          selected: {_selectedChartType},
          onSelectionChanged: (Set<int> selection) {
            setState(() {
              _selectedChartType = selection.first;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSelectedChart() {
    switch (_selectedChartType) {
      case 0:
        return _buildBarChart();
      case 1:
        return _buildPieChart();
      case 2:
        return _buildLineChart();
      default:
        return _buildBarChart();
    }
  }

  Widget _buildBarChart() {
    final sortedStats = List<AppStatistics>.from(widget.statistics)
      ..sort((a, b) => b.totalDuration.compareTo(a.totalDuration));

    final topApps = sortedStats.take(10).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: topApps.isNotEmpty ? topApps.first.totalDuration.toDouble() * 1.2 : 100,
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final app = topApps[group.x];
              return BarTooltipItem(
                '${app.app.name}\n${_formatDuration(app.totalDuration)}',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12.sp,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() < topApps.length) {
                  final app = topApps[value.toInt()];
                  return Padding(
                    padding: EdgeInsets.only(top: UIConstants.spacingS.h),
                    child: Text(
                      (app.app.name ?? 'Unknown').length > 8
                          ? '${(app.app.name ?? 'Unknown').substring(0, 8)}...'
                          : app.app.name ?? 'Unknown',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  _formatDurationShort(value.toInt()),
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: topApps.asMap().entries.map((entry) {
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: entry.value.totalDuration.toDouble(),
                color: Theme.of(context).colorScheme.primary,
                width: 16.w,
                borderRadius: BorderRadius.circular(UIConstants.radiusS),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPieChart() {
    final sortedStats = List<AppStatistics>.from(widget.statistics)
      ..sort((a, b) => b.totalDuration.compareTo(a.totalDuration));

    final topApps = sortedStats.take(8).toList();
    final colors = [
      Theme.of(context).colorScheme.primary,
      Theme.of(context).colorScheme.secondary,
      Theme.of(context).colorScheme.tertiary,
      Colors.orange,
      Colors.green,
      Colors.purple,
      Colors.red,
      Colors.teal,
    ];

    return PieChart(
      PieChartData(
        sections: topApps.asMap().entries.map((entry) {
          final index = entry.key;
          final stat = entry.value;
          return PieChartSectionData(
            color: colors[index % colors.length],
            value: stat.totalDuration.toDouble(),
            title: '${((stat.totalDuration / widget.statistics.fold(0, (sum, s) => sum + s.totalDuration)) * 100).toStringAsFixed(1)}%',
            radius: 60.r,
            titleStyle: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        }).toList(),
        centerSpaceRadius: 40.r,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildLineChart() {
    // For now, show a simple line chart with daily usage
    // In a real implementation, you'd use timeline data
    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  'Day ${value.toInt() + 1}',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  _formatDurationShort(value.toInt()),
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(7, (index) {
              // Mock data for the last 7 days
              final value = (widget.statistics.isNotEmpty
                  ? widget.statistics.first.todayDuration
                  : 0) * (0.5 + (index * 0.1));
              return FlSpot(index.toDouble(), value.toDouble());
            }),
            isCurved: true,
            color: Theme.of(context).colorScheme.primary,
            barWidth: 3.w,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }



  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  String _formatDurationShort(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      return '${hours}h';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../providers/checkpoint_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';

// Import the new components
import 'app_detail/app_detail_enums.dart';
import 'app_detail/app_hero_banner.dart';
import 'app_detail/app_stats_row.dart';
import 'app_detail/longest_session_card.dart';
import 'app_detail/time_range_selector.dart';
import 'app_detail/checkpoint_selector.dart';
import 'app_detail/checkpoint_stats_card.dart';
import 'app_detail/sessions_section.dart';
import 'app_detail/statistics_section.dart';
import 'checkpoint_dialog.dart';

class AppDetailView extends ConsumerStatefulWidget {
  final AppModel app;

  const AppDetailView({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<AppDetailView> createState() => _AppDetailViewState();
}

class _AppDetailViewState extends ConsumerState<AppDetailView> {
  TimeRange _selectedTimeRange = TimeRange.lastSevenDays;
  CheckpointModel? _selectedCheckpoint;
  SessionSortOption _selectedSortOption = SessionSortOption.durationLongest;
  bool _showAllSessions = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTimelineData();
    });
  }

  void _loadTimelineData() {
    final dateRange = _selectedTimeRange.getDateRange();
    ref.read(timelineProvider.notifier).loadTimeline(
      startDate: dateRange?.start,
      endDate: dateRange?.end,
      appId: widget.app.id,
    );
  }

  void _refreshCheckpointData() {
    invalidateCheckpointCache(ref, widget.app.id);

    ref.read(checkpointsForAppProvider(widget.app.id).notifier).refresh();
    ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).refresh();
  }

  void _onTimeRangeChanged(TimeRange newRange) {
    setState(() {
      _selectedTimeRange = newRange;
      _showAllSessions = false;
    });
    _loadTimelineData();
  }

  void _onCheckpointChanged(CheckpointModel? checkpoint) {
    setState(() {
      _selectedCheckpoint = checkpoint;
      _showAllSessions = false;
    });
  }

  void _onSortOptionChanged(SessionSortOption newOption) {
    setState(() => _selectedSortOption = newOption);
  }

  void _onToggleShowAllSessions() {
    setState(() => _showAllSessions = !_showAllSessions);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          AppHeroBanner(
            app: widget.app,
            onEdit: () => _showEditDialog(context),
            onDelete: () => _showDeleteConfirmation(context),
          ),
          SliverToBoxAdapter(
            child: ModernContainer(
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  child: Padding(
                    padding: const EdgeInsets.all(DesignTokens.spacingL),
                    child: ModernColumn(
                      spacing: DesignTokens.spacingL,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppStatsRow(app: widget.app),
                        LongestSessionCard(app: widget.app),
                        _buildFiltersSection(theme, isDark),
                        if (_selectedCheckpoint != null)
                          CheckpointStatsCard(
                            checkpoint: _selectedCheckpoint!,
                            appId: widget.app.id.toString(),
                          ),
                        SessionsSection(
                          appId: widget.app.id.toString(),
                          selectedTimeRange: _selectedTimeRange,
                          selectedCheckpoint: _selectedCheckpoint,
                          selectedSortOption: _selectedSortOption,
                          showAllSessions: _showAllSessions,
                          onSortChanged: _onSortOptionChanged,
                          onToggleShowAll: _onToggleShowAllSessions,
                        ),

                        StatisticsSection(
                          appId: widget.app.id.toString(),
                          selectedTimeRange: _selectedTimeRange,
                          selectedCheckpoint: _selectedCheckpoint,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(ThemeData theme, bool isDark) {
    return ModernColumn(
      spacing: DesignTokens.spacingM,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filters',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w600,
          ),
        ),
        ModernRow(
          spacing: DesignTokens.spacingL,
          children: [
            Expanded(
              child: ModernColumn(
                spacing: DesignTokens.spacingS,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Time Range',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TimeRangeSelector(
                    selectedTimeRange: _selectedTimeRange,
                    onChanged: _onTimeRangeChanged,
                  ),
                ],
              ),
            ),
            Expanded(
              child: ModernColumn(
                spacing: DesignTokens.spacingS,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Checkpoint',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  CheckpointSelector(
                    app: widget.app,
                    selectedCheckpoint: _selectedCheckpoint,
                    onChanged: _onCheckpointChanged,
                  ),
                ],
              ),
            ),
          ],
        ),
        if (_selectedCheckpoint != null || _selectedTimeRange != TimeRange.allTime) ...[
          SizedBox(height: UIConstants.spacingM.h),
        ],
      ],
    );
  }



  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CheckpointDialog(app: widget.app),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        title: Text(
          'Delete App',
          style: theme.textTheme.titleLarge?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${widget.app.name}"? This action cannot be undone.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
          ),
        ),
        actions: [
          OutlinedModernButton(
            onPressed: () => Navigator.of(context).pop(),
            size: ButtonSize.small,
            child: const Text('Cancel'),
          ),
          DangerButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(widget.app.id);
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            size: ButtonSize.small,
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

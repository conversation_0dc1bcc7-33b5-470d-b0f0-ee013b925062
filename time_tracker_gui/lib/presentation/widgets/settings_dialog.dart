import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/settings_provider.dart';
import '../providers/theme_provider.dart';
import '../../app/design_system/design_system.dart';
import '../../data/services/api_service.dart';
import 'cache_settings_widget.dart';

class SettingsDialog extends ConsumerStatefulWidget {
  const SettingsDialog({super.key});

  @override
  ConsumerState<SettingsDialog> createState() => _SettingsDialogState();
}

class _SettingsDialogState extends ConsumerState<SettingsDialog> with TickerProviderStateMixin {
  late TextEditingController _backendUrlController;
  late TextEditingController _refreshIntervalController;
  late TabController _tabController;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    final settings = ref.read(settingsProvider);
    _backendUrlController = TextEditingController(text: settings.backendUrl);
    _refreshIntervalController = TextEditingController(text: settings.refreshInterval.toString());

    // Add listeners to detect changes
    _backendUrlController.addListener(_onTextChanged);
    _refreshIntervalController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    final settings = ref.read(settingsProvider);
    final hasChanges = _backendUrlController.text.trim() != settings.backendUrl ||
        _refreshIntervalController.text.trim() != settings.refreshInterval.toString();

    if (hasChanges != _hasUnsavedChanges) {
      setState(() => _hasUnsavedChanges = hasChanges);
    }
  }

  @override
  void dispose() {
    _backendUrlController.removeListener(_onTextChanged);
    _refreshIntervalController.removeListener(_onTextChanged);
    _backendUrlController.dispose();
    _refreshIntervalController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: ModernContainer(
        width: 800, // Increased width for better usability
        height: 700,
        child: ModernColumn(
          spacing: 0,
          children: [
            // Header with tabs
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingL),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: ModernColumn(
                spacing: DesignTokens.spacingM,
                children: [
                  ModernRow(
                    spacing: DesignTokens.spacingS,
                    children: [
                      Icon(
                        Icons.settings,
                        color: theme.colorScheme.primary,
                      ),
                      const Text(
                        'Settings',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  TabBar(
                    controller: _tabController,
                    tabs: const [
                      Tab(
                        icon: Icon(Icons.tune),
                        text: 'General',
                      ),
                      Tab(
                        icon: Icon(Icons.storage),
                        text: 'Cache',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildGeneralSettings(),
                  _buildCacheSettings(),
                ],
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingL),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: ModernRow(
                mainAxisAlignment: MainAxisAlignment.end,
                spacing: DesignTokens.spacingM,
                children: [
                  SecondaryButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  if (_hasUnsavedChanges) ...[
                    OutlinedModernButton(
                      onPressed: () {
                        // Reset changes
                        final settings = ref.read(settingsProvider);
                        _backendUrlController.text = settings.backendUrl;
                        _refreshIntervalController.text = settings.refreshInterval.toString();
                        setState(() => _hasUnsavedChanges = false);
                      },
                      child: const Text('Discard'),
                    ),
                    PrimaryButton(
                      onPressed: _saveSettings,
                      child: const Text('Save'),
                    ),
                  ] else
                    PrimaryButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSettings() {
    final settings = ref.watch(settingsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Backend Configuration'),
          _buildBackendUrlField(),
          const SizedBox(height: DesignTokens.spacingM),

          _buildSectionTitle('Appearance'),
          _buildThemeSelector(),
          const SizedBox(height: DesignTokens.spacingM),

          _buildSectionTitle('Behavior'),
          _buildNotificationToggle(settings),
          _buildAutoStartToggle(settings),
          _buildRefreshIntervalField(),
          const SizedBox(height: DesignTokens.spacingM),

          _buildSectionTitle('Actions'),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildCacheSettings() {
    return const SingleChildScrollView(
      child: CacheSettingsWidget(),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: DesignTokens.spacingS),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildBackendUrlField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _backendUrlController,
          decoration: const InputDecoration(
            labelText: 'Backend URL',
            hintText: 'http://localhost:8080',
            prefixIcon: Icon(Icons.link),
          ),
        ),
        const SizedBox(height: DesignTokens.spacingXS),
        Text(
          'The URL of your time tracker backend service',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildThemeSelector() {
    final currentTheme = ref.watch(themeModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Theme',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: DesignTokens.spacingS),
        Row(
          children: [
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('Light'),
                value: ThemeMode.light,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('Dark'),
                value: ThemeMode.dark,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('System'),
                value: ThemeMode.system,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotificationToggle(AppSettings settings) {
    return SwitchListTile(
      title: const Text('Enable Notifications'),
      subtitle: const Text('Show notifications for tracking events'),
      value: settings.enableNotifications,
      onChanged: (value) {
        ref.read(settingsProvider.notifier).updateNotifications(value);
      },
    );
  }

  Widget _buildAutoStartToggle(AppSettings settings) {
    return SwitchListTile(
      title: const Text('Auto-start Tracking'),
      subtitle: const Text('Automatically start tracking when app launches'),
      value: settings.enableAutoStart,
      onChanged: (value) {
        ref.read(settingsProvider.notifier).updateAutoStart(value);
      },
    );
  }

  Widget _buildRefreshIntervalField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _refreshIntervalController,
          decoration: const InputDecoration(
            labelText: 'Refresh Interval (seconds)',
            hintText: '5',
            prefixIcon: Icon(Icons.refresh),
          ),
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: DesignTokens.spacingXS),
        Text(
          'How often to refresh data from the backend',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _testConnection,
            icon: const Icon(Icons.wifi_protected_setup),
            label: const Text('Test Connection'),
          ),
        ),
        const SizedBox(height: DesignTokens.spacingS),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _resetToDefaults,
            icon: const Icon(Icons.restore),
            label: const Text('Reset to Defaults'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
              side: BorderSide(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ),
      ],
    );
  }

  void _saveSettings() async {
    final url = _backendUrlController.text.trim();
    final intervalText = _refreshIntervalController.text.trim();

    if (url.isNotEmpty && !ref.read(settingsProvider.notifier).isValidUrl(url)) {
      _showErrorSnackBar('Please enter a valid URL');
      return;
    }

    final interval = int.tryParse(intervalText);
    if (interval == null || interval < 1 || interval > 300) {
      _showErrorSnackBar('Refresh interval must be between 1 and 300 seconds');
      return;
    }

    try {
      await ref.read(settingsProvider.notifier).updateBackendUrl(url);
      await ref.read(settingsProvider.notifier).updateRefreshInterval(interval);

      setState(() => _hasUnsavedChanges = false);
      _showSuccessSnackBar('Settings saved successfully');

      // Close dialog after a short delay to show the success message
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    }
  }

  void _testConnection() async {
    final url = _backendUrlController.text.trim();
    if (url.isEmpty || !ref.read(settingsProvider.notifier).isValidUrl(url)) {
      _showErrorSnackBar('Please enter a valid URL first');
      return;
    }
    
    try {
      // Show loading state
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 16),
              Text('Testing connection...'),
            ],
          ),
          duration: Duration(seconds: 30),
        ),
      );
      
      // Test connection by trying to fetch apps
      final testApiService = ApiService(baseUrl: url);
      await testApiService.getAllApps();
      
      // If we get here, connection was successful
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _showSuccessSnackBar('Connection successful! Backend is reachable.');
    } catch (e) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _showErrorSnackBar('Connection failed: ${e.toString()}');
    }
  }

  void _resetToDefaults() async {
    final confirmed = await _showConfirmationDialog(
      'Reset Settings',
      'Are you sure you want to reset all settings to their default values?',
    );

    if (confirmed) {
      try {
        await ref.read(settingsProvider.notifier).resetToDefaults();
        final settings = ref.read(settingsProvider);
        _backendUrlController.text = settings.backendUrl;
        _refreshIntervalController.text = settings.refreshInterval.toString();
        setState(() => _hasUnsavedChanges = false);
        _showSuccessSnackBar('Settings reset to defaults');
      } catch (e) {
        _showErrorSnackBar('Failed to reset settings: $e');
      }
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }
}

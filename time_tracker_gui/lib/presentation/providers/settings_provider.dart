import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

// Settings State
class AppSettings {
  final String backendUrl;
  final bool enableNotifications;
  final bool enableAutoStart;
  final int refreshInterval;
  final String theme;

  const AppSettings({
    this.backendUrl = 'http://localhost:6754',
    this.enableNotifications = true,
    this.enableAutoStart = false,
    this.refreshInterval = 5,
    this.theme = 'system',
  });

  AppSettings copyWith({
    String? backendUrl,
    bool? enableNotifications,
    bool? enableAutoStart,
    int? refreshInterval,
    String? theme,
  }) {
    return AppSettings(
      backendUrl: backendUrl ?? this.backendUrl,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableAutoStart: enableAutoStart ?? this.enableAutoStart,
      refreshInterval: refreshInterval ?? this.refreshInterval,
      theme: theme ?? this.theme,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'backendUrl': backendUrl,
      'enableNotifications': enableNotifications,
      'enableAutoStart': enableAutoStart,
      'refreshInterval': refreshInterval,
      'theme': theme,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      backendUrl: json['backendUrl'] ?? 'http://localhost:6754',
      enableNotifications: json['enableNotifications'] ?? true,
      enableAutoStart: json['enableAutoStart'] ?? false,
      refreshInterval: json['refreshInterval'] ?? 5,
      theme: json['theme'] ?? 'system',
    );
  }
}

// Settings Provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>((ref) {
  return SettingsNotifier();
});

class SettingsNotifier extends StateNotifier<AppSettings> {
  static const String _settingsKey = 'app_settings';

  SettingsNotifier() : super(const AppSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      print('Loading settings from SharedPreferences...');
      print('Raw settings JSON: $settingsJson');

      if (settingsJson != null) {
        final Map<String, dynamic> json = jsonDecode(settingsJson);
        print('Parsed settings JSON: $json');
        state = AppSettings.fromJson(json);
        print('Loaded settings: backendUrl=${state.backendUrl}, refreshInterval=${state.refreshInterval}');
      } else {
        print('No saved settings found, using defaults');
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Failed to load settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());

      print('Saving settings to SharedPreferences...');
      print('Settings JSON: $jsonString');

      await prefs.setString(_settingsKey, jsonString);
      print('Settings saved successfully');
    } catch (e) {
      print('Failed to save settings: $e');
    }
  }

  Future<void> updateBackendUrl(String url) async {
    state = state.copyWith(backendUrl: url);
    await _saveSettings();
  }

  Future<void> updateNotifications(bool enabled) async {
    state = state.copyWith(enableNotifications: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoStart(bool enabled) async {
    state = state.copyWith(enableAutoStart: enabled);
    await _saveSettings();
  }

  Future<void> updateRefreshInterval(int interval) async {
    state = state.copyWith(refreshInterval: interval);
    await _saveSettings();
  }

  Future<void> updateTheme(String theme) async {
    state = state.copyWith(theme: theme);
    await _saveSettings();
  }

  Future<void> resetToDefaults() async {
    state = const AppSettings();
    await _saveSettings();
  }

  bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}

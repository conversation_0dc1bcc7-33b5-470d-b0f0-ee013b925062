import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../data/services/poster_cache_service.dart';
import 'app_providers.dart';
import 'local_game_poster_providers.dart';

class RotatingPosterState {
  final Poster? currentPoster;
  final AppModel? currentApp;
  final bool isLoading;

  const RotatingPosterState({
    this.currentPoster,
    this.currentApp,
    this.isLoading = false,
  });

  RotatingPosterState copyWith({
    Poster? currentPoster,
    AppModel? currentApp,
    bool? isLoading,
  }) {
    return RotatingPosterState(
      currentPoster: currentPoster ?? this.currentPoster,
      currentApp: currentApp ?? this.currentApp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class RotatingPosterNotifier extends StateNotifier<RotatingPosterState> {
  final Ref _ref;
  Timer? _rotationTimer;
  final Random _random = Random();

  RotatingPosterNotifier(this._ref) : super(const RotatingPosterState(isLoading: true)) {
    _loadImmediatePoster();
    _startRotation();

    // Listen for apps to become available and load poster immediately
    _ref.listen(appsProvider, (previous, next) {
      if (next.hasValue && next.value!.isNotEmpty && state.currentPoster == null) {
        _loadImmediatePoster();
      }
    });
  }

  @override
  void dispose() {
    _rotationTimer?.cancel();
    super.dispose();
  }

  // Load a poster immediately if available in cache
  void _loadImmediatePoster() async {
    try {
      final appsAsyncValue = _ref.read(appsProvider);

      // Only proceed if apps are already loaded
      if (appsAsyncValue.hasValue && appsAsyncValue.value!.isNotEmpty) {
        final apps = appsAsyncValue.value!;
        final validApps = apps.where((app) =>
          (app.productName?.isNotEmpty ?? false) || (app.name?.isNotEmpty ?? false)
        ).toList();

        if (validApps.isNotEmpty) {
          final cacheService = _ref.read(posterCacheServiceProvider);

          // Check for any cached poster and display it immediately
          for (final app in validApps.take(10)) { // Check first 10 apps
            final gameName = _getGameName(app);
            final cachedPoster = await cacheService.getCachedPoster(gameName);
            if (cachedPoster != null && cachedPoster.hasImage) {
              print('RotatingPoster: Immediate load - found cached poster for $gameName');
              state = RotatingPosterState(
                currentPoster: cachedPoster,
                currentApp: app, // IMPORTANT: Set the current app so image can be loaded
                isLoading: false,
              );
              return; // Exit immediately after finding first poster
            }
          }
        }
      }

      // If no immediate poster found, keep loading state
      print('RotatingPoster: No immediate poster available, will load when apps are ready');
    } catch (e) {
      print('RotatingPoster: Error in immediate load: $e');
    }
  }

  void _startRotation() {
    _loadRandomPoster();
    _rotationTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _loadRandomPoster();
    });
  }

  Future<void> _loadRandomPoster() async {
    try {
      state = state.copyWith(isLoading: true);

      final appsAsyncValue = _ref.read(appsProvider);

      await appsAsyncValue.when(
        data: (apps) async {
          if (apps.isEmpty) {
            print('RotatingPoster: No apps available');
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          // Filter apps that have valid names for poster lookup
          final validApps = apps.where((app) {
            final gameName = _getGameName(app);
            return gameName.isNotEmpty;
          }).toList();

          if (validApps.isEmpty) {
            print('RotatingPoster: No valid apps for poster lookup');
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          print('RotatingPoster: Found ${validApps.length} valid apps');

          // Get cache service
          final cacheService = _ref.read(posterCacheServiceProvider);

          // First, try to find apps that already have cached posters
          final List<AppModel> appsWithPosters = [];
          for (final app in validApps) {
            final gameName = _getGameName(app);
            final cachedPoster = await cacheService.getCachedPoster(gameName);
            if (cachedPoster != null && cachedPoster.hasImage) {
              appsWithPosters.add(app);
            }
          }

          print('RotatingPoster: Found ${appsWithPosters.length} apps with cached posters');

          // If no cached posters exist, try to fetch one FAST
          if (appsWithPosters.isEmpty) {
            print('RotatingPoster: No cached posters found, attempting fast fetch...');
            final firstPoster = await _fetchFirstPosterFast(validApps, cacheService);
            if (firstPoster != null) {
              // Find the app that matches this poster
              for (final app in validApps) {
                final gameName = _getGameName(app);
                final cachedPoster = await cacheService.getCachedPoster(gameName);
                if (cachedPoster != null && cachedPoster.hasImage) {
                  appsWithPosters.add(app);
                  break; // We only need one for immediate display
                }
              }
            }
          }

          if (appsWithPosters.isEmpty) {
            print('RotatingPoster: Still no posters available after fetch attempt');
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          // Select a random app from those with cached posters
          final randomApp = appsWithPosters[_random.nextInt(appsWithPosters.length)];
          final gameName = _getGameName(randomApp);

          // Get the cached poster (we know it exists)
          final poster = await cacheService.getCachedPoster(gameName);

          print('RotatingPoster: Selected poster for ${gameName}');

          state = RotatingPosterState(
            currentPoster: poster,
            currentApp: randomApp,
            isLoading: false,
          );
        },
        loading: () {
          print('RotatingPoster: Apps still loading');
          state = const RotatingPosterState(isLoading: true);
        },
        error: (error, stackTrace) {
          print('RotatingPoster: Error loading apps: $error');
          state = const RotatingPosterState(isLoading: false);
        },
      );
    } catch (e) {
      print('RotatingPoster: Exception in _loadRandomPoster: $e');
      state = const RotatingPosterState(isLoading: false);
    }
  }

  // Fast fetch: Try to get the first available poster as quickly as possible
  Future<Poster?> _fetchFirstPosterFast(List<AppModel> apps, PosterCacheService cacheService) async {
    print('RotatingPoster: Fast fetching first poster from ${apps.length} apps');

    // Try multiple apps in parallel, return as soon as we get one
    final futures = apps.take(3).map((app) async {
      try {
        final gameName = _getGameName(app);
        print('RotatingPoster: Fast fetching poster for $gameName');

        // Use a faster method that doesn't cache images immediately
        final poster = await cacheService.searchAndCachePosterFast(gameName);
        if (poster != null) {
          print('RotatingPoster: Fast fetch success for $gameName');
          return poster;
        }
      } catch (e) {
        print('RotatingPoster: Fast fetch error for ${_getGameName(app)}: $e');
      }
      return null;
    });

    // Return the first successful result
    final results = await Future.wait(futures);
    return results.firstWhere((poster) => poster != null, orElse: () => null);
  }

  String _getGameName(AppModel app) {
    String gameName = '';

    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    // Clean up the game name for better search results
    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }

  void forceRotate() {
    _loadRandomPoster();
  }
}

final rotatingPosterProvider = StateNotifierProvider<RotatingPosterNotifier, RotatingPosterState>((ref) {
  return RotatingPosterNotifier(ref);
});
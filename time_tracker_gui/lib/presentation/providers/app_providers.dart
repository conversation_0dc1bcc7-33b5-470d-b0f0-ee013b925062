import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import '../../data/repositories/mock_time_tracker_repository.dart';
import '../../data/services/websocket_service.dart';
import 'settings_provider.dart';

// Service Providers
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  final settings = ref.watch(settingsProvider);
  final wsUrl = settings.backendUrl.replaceFirst('http', 'ws') + '/ws';
  print('Creating WebSocketService with wsUrl: $wsUrl');

  final webSocketService = WebSocketService(wsUrl: wsUrl);

  ref.onDispose(() {
    print('Disposing WebSocketService');
    webSocketService.dispose();
  });

  return webSocketService;
});

// Repository provider - can switch between mock and real implementation
final timeTrackerRepositoryProvider = Provider<TimeTrackerRepository>((ref) {
  const useMockBackend = false;

  if (useMockBackend) {
    return MockTimeTrackerRepository();
  } else {
    final webSocketService = ref.watch(webSocketServiceProvider);

    print('Creating WebSocketTimeTrackerRepository');

    final repository = WebSocketTimeTrackerRepository(
      webSocketService: webSocketService,
    );

    ref.onDispose(() {
      print('Disposing WebSocketTimeTrackerRepository');
      repository.dispose();
    });

    return repository;
  }
});

// Settings change listener that invalidates providers
final settingsChangeProvider = Provider<void>((ref) {
  ref.listen(settingsProvider, (previous, next) {
    if (previous != null && previous.backendUrl != next.backendUrl) {
      print('Settings changed: ${previous.backendUrl} -> ${next.backendUrl}');
      ref.invalidate(webSocketServiceProvider);
      ref.invalidate(timeTrackerRepositoryProvider);
    }
  });
});

// State Providers
final appsProvider = StateNotifierProvider<AppsNotifier, AsyncValue<List<AppModel>>>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return AppsNotifier(repository);
});

final trackingStatusProvider = StateNotifierProvider<TrackingStatusNotifier, AsyncValue<TrackingStatus>>((ref) {
  return TrackingStatusNotifier(ref.watch(timeTrackerRepositoryProvider));
});

final statisticsProvider = StateNotifierProvider<StatisticsNotifier, AsyncValue<List<AppStatistics>>>((ref) {
  return StatisticsNotifier(ref.read(timeTrackerRepositoryProvider));
});

final timelineProvider = StateNotifierProvider<TimelineNotifier, AsyncValue<List<TimelineModel>>>((ref) {
  return TimelineNotifier(ref.read(timeTrackerRepositoryProvider));
});

// Separate provider for all-time timeline data (for session counting in app list)
final allTimeTimelineProvider = StateNotifierProvider<AllTimeTimelineNotifier, AsyncValue<List<TimelineModel>>>((ref) {
  return AllTimeTimelineNotifier(ref.read(timeTrackerRepositoryProvider));
});

// More efficient provider for session counts per app
final sessionCountsProvider = StateNotifierProvider<SessionCountsNotifier, AsyncValue<Map<int, int>>>((ref) {
  return SessionCountsNotifier(ref.read(timeTrackerRepositoryProvider));
});

// Connection Status Provider
final connectionStatusProvider = StateNotifierProvider<ConnectionStatusNotifier, ConnectionStatus>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  final notifier = ConnectionStatusNotifier(repository);

  // Listen for repository changes and update the notifier
  ref.listen(timeTrackerRepositoryProvider, (previous, next) {
    if (previous != next) {
      notifier.updateRepository(next);
    }
  });

  return notifier;
});

// Search and Filter Providers
final appSearchProvider = StateNotifierProvider<AppSearchNotifier, AppSearchState>((ref) {
  return AppSearchNotifier();
});

// Enhanced app model with actual last used date
class AppWithLastUsed {
  final AppModel app;
  final DateTime? lastUsedDate;

  const AppWithLastUsed({
    required this.app,
    this.lastUsedDate,
  });
}

// Provider that combines apps with their actual last used dates
final appsWithLastUsedProvider = Provider<AsyncValue<List<AppWithLastUsed>>>((ref) {
  final apps = ref.watch(appsProvider);
  final allTimeTimeline = ref.watch(allTimeTimelineProvider);

  return apps.when(
    data: (appList) {
      return allTimeTimeline.when(
        data: (timelineList) {
          final appsWithLastUsed = appList.map((app) {
            // Find the most recent timeline entry for this app
            final appTimelineEntries = timelineList
                .where((timeline) => timeline.appId == app.id)
                .toList();

            DateTime? lastUsedDate;
            if (appTimelineEntries.isNotEmpty) {
              // Sort by date descending and take the first (most recent)
              appTimelineEntries.sort((a, b) {
                final dateA = a.date ?? DateTime(1970);
                final dateB = b.date ?? DateTime(1970);
                return dateB.compareTo(dateA);
              });
              lastUsedDate = appTimelineEntries.first.date;
            }

            return AppWithLastUsed(
              app: app,
              lastUsedDate: lastUsedDate,
            );
          }).toList();

          return AsyncValue.data(appsWithLastUsed);
        },
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

final filteredAppsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final appsWithLastUsed = ref.watch(appsWithLastUsedProvider);
  final searchState = ref.watch(appSearchProvider);

  return appsWithLastUsed.when(
    data: (appWithLastUsedList) {
      var filteredList = appWithLastUsedList;

      // Apply search filter
      if (searchState.searchQuery.isNotEmpty) {
        filteredList = filteredList.where((appWithLastUsed) {
          final app = appWithLastUsed.app;
          final query = searchState.searchQuery.toLowerCase();
          return (app.name?.toLowerCase().contains(query) ?? false) ||
                 (app.productName?.toLowerCase().contains(query) ?? false);
        }).toList();
      }

      // Apply sorting
      switch (searchState.sortBy) {
        case AppSortBy.name:
          filteredList.sort((a, b) => (a.app.name ?? '').compareTo(b.app.name ?? ''));
          break;
        case AppSortBy.duration:
          filteredList.sort((a, b) => (b.app.duration ?? 0).compareTo(a.app.duration ?? 0));
          break;
        case AppSortBy.launches:
          filteredList.sort((a, b) => (b.app.launches ?? 0).compareTo(a.app.launches ?? 0));
          break;
        case AppSortBy.lastUsed:
          filteredList.sort((a, b) {
            if (a.lastUsedDate == null && b.lastUsedDate == null) return 0;
            if (a.lastUsedDate == null) return 1;
            if (b.lastUsedDate == null) return -1;
            return b.lastUsedDate!.compareTo(a.lastUsedDate!);
          });
          break;
      }

      return AsyncValue.data(filteredList.map((appWithLastUsed) => appWithLastUsed.app).toList());
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// State Notifiers
class AppsNotifier extends StateNotifier<AsyncValue<List<AppModel>>> {
  final TimeTrackerRepository _repository;

  AppsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadApps();
    _listenToAppUpdates();
    _listenToAppsListUpdates();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> loadApps() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      if (_repository is WebSocketTimeTrackerRepository) {
        final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
        await webSocketRepo.connect();

        await webSocketRepo.getAllApps();
      } else {
        final apps = await _repository.getAllApps();
        if (mounted) {
          state = AsyncValue.data(apps);
        }
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> addApp(String name) async {
    try {
      await _repository.insertApp(name);
      // For WebSocket, the updated list will come through the stream
      if (_repository is! WebSocketTimeTrackerRepository) {
        await loadApps(); // Refresh the list for non-WebSocket repositories
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> deleteApp(int appId) async {
    try {
      await _repository.deleteApp(appId);
      // For WebSocket, the updated list will come through the stream
      if (_repository is! WebSocketTimeTrackerRepository) {
        await loadApps(); // Refresh the list for non-WebSocket repositories
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  void _listenToAppUpdates() {
    _repository.appUpdateStream.listen((updatedApp) {
      if (!mounted) return;
      state.whenData((apps) {
        final updatedApps = apps.map((app) {
          return app.id == updatedApp.id ? updatedApp : app;
        }).toList();
        if (mounted) {
          state = AsyncValue.data(updatedApps);
        }
      });
    });
  }

  void _listenToAppsListUpdates() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;

      webSocketRepo.appsListStream.listen(
        (apps) {
          final newState = AsyncValue.data(apps);
          if (mounted) {
            state = newState;
          }
        },
        onError: (error) {
          print('AppsNotifier: Error in apps list stream: $error');
          if (mounted) {
            state = AsyncValue.error(error, StackTrace.current);
          }
        },
        onDone: () {
          print('AppsNotifier: Apps list stream closed');
        },
      );
    }
  }
}

class TrackingStatusNotifier extends StateNotifier<AsyncValue<TrackingStatus>> {
  final TimeTrackerRepository _repository;

  TrackingStatusNotifier(this._repository) : super(const AsyncValue.loading()) {
    _initializeRepository();
    loadTrackingStatus();
    _listenToStatusUpdates();
  }

  Future<void> _initializeRepository() async {
    try {
      await _repository.connect();
    } catch (e) {
      print('Failed to connect repository: $e');
    }
  }

  Future<void> loadTrackingStatus() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();
      final status = await _repository.getTrackingStatus();
      if (mounted) {
        state = AsyncValue.data(status);
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> startTracking() async {
    try {
      await _repository.startTracking();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> stopTracking() async {
    try {
      await _repository.stopTracking();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> pauseTracking() async {
    try {
      await _repository.pauseTracking();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> resumeTracking() async {
    try {
      await _repository.resumeTracking();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  void _listenToStatusUpdates() {
    _repository.trackingStatusStream.listen((status) {
      if (mounted) {
        state = AsyncValue.data(status);
      }
    });
  }
}

class StatisticsNotifier extends StateNotifier<AsyncValue<List<AppStatistics>>> {
  final TimeTrackerRepository _repository;

  StatisticsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadStatistics();
    _listenToStatisticsUpdates();
  }

  Future<void> loadStatistics({DateTime? startDate, DateTime? endDate}) async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      if (_repository is WebSocketTimeTrackerRepository) {
        await _repository.getStatistics(startDate: startDate, endDate: endDate);
      } else {
        final statistics = await _repository.getStatistics(startDate: startDate, endDate: endDate);
        if (mounted) {
          state = AsyncValue.data(statistics);
        }
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  void _listenToStatisticsUpdates() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
      webSocketRepo.statisticsStream.listen((statistics) {
        if (mounted) {
          state = AsyncValue.data(statistics);
        }
      });
    }
  }
}

class TimelineNotifier extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  final TimeTrackerRepository _repository;

  TimelineNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTimeline();
    _listenToTimelineUpdates();
  }

  Future<void> loadTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      if (_repository is WebSocketTimeTrackerRepository) {
        await _repository.getTimeline(startDate: startDate, endDate: endDate, appId: appId);
      } else {
        final timeline = await _repository.getTimeline(startDate: startDate, endDate: endDate, appId: appId);
        if (mounted) {
          state = AsyncValue.data(timeline);
        }
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  void _listenToTimelineUpdates() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
      webSocketRepo.timelineStream.listen((timeline) {
        if (mounted) {
          state = AsyncValue.data(timeline);
        }
      });
    }
  }
}

class AllTimeTimelineNotifier extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  final TimeTrackerRepository _repository;

  AllTimeTimelineNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadAllTimeTimeline();
    _listenToTimelineUpdates();
  }

  Future<void> loadAllTimeTimeline() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      if (_repository is WebSocketTimeTrackerRepository) {
        await _repository.getTimeline();
      } else {
        final timeline = await _repository.getTimeline();
        if (mounted) {
          state = AsyncValue.data(timeline);
        }
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> refresh() async {
    await loadAllTimeTimeline();
  }

  void _listenToTimelineUpdates() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
      webSocketRepo.timelineStream.listen((timeline) {
        if (mounted) {
          state = AsyncValue.data(timeline);
        }
      });
    }
  }
}

class SessionCountsNotifier extends StateNotifier<AsyncValue<Map<int, int>>> {
  final TimeTrackerRepository _repository;

  SessionCountsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadSessionCounts();
    _listenToSessionCountsUpdates();
  }

  Future<void> loadSessionCounts() async {
    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      if (_repository is WebSocketTimeTrackerRepository) {
        await _repository.getSessionCounts();
      } else {
        final sessionCounts = await _repository.getSessionCounts();
        if (mounted) {
          state = AsyncValue.data(sessionCounts);
        }
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> refresh() async {
    await loadSessionCounts();
  }

  void _listenToSessionCountsUpdates() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
      webSocketRepo.sessionCountsStream.listen((counts) {
        if (mounted) {
          state = AsyncValue.data(counts);
        }
      });
    }
  }
}

// Search and Filter State
enum AppSortBy { name, duration, launches, lastUsed }
enum SortOrder { ascending, descending }

class AppSearchState {
  final String searchQuery;
  final AppSortBy sortBy;
  final SortOrder sortOrder;

  const AppSearchState({
    this.searchQuery = '',
    this.sortBy = AppSortBy.name,
    this.sortOrder = SortOrder.ascending,
  });

  AppSearchState copyWith({
    String? searchQuery,
    AppSortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return AppSearchState(
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'searchQuery': searchQuery,
      'sortBy': sortBy.index,
      'sortOrder': sortOrder.index,
    };
  }

  factory AppSearchState.fromJson(Map<String, dynamic> json) {
    return AppSearchState(
      searchQuery: json['searchQuery'] ?? '',
      sortBy: AppSortBy.values[json['sortBy'] ?? 0],
      sortOrder: SortOrder.values[json['sortOrder'] ?? 0],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSearchState &&
        other.searchQuery == searchQuery &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder;
  }

  @override
  int get hashCode {
    return Object.hash(searchQuery, sortBy, sortOrder);
  }
}

class AppSearchNotifier extends StateNotifier<AppSearchState> {
  static const String _appSearchKey = 'app_search_state';
  Timer? _saveTimer;

  AppSearchNotifier() : super(const AppSearchState()) {
    _loadSearchState();
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searchJson = prefs.getString(_appSearchKey);

      if (searchJson != null) {
        final Map<String, dynamic> json = jsonDecode(searchJson);
        state = AppSearchState.fromJson(json);
      }
    } catch (e) {
      // If loading fails, keep default state
      print('Failed to load app search state: $e');
    }
  }

  Future<void> _saveSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_appSearchKey, jsonString);
    } catch (e) {
      print('Failed to save app search state: $e');
    }
  }

  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), () {
      _saveSearchState();
    });
  }

  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _debouncedSave();
  }

  void updateSortBy(AppSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
    _debouncedSave();
  }

  void updateSortOrder(SortOrder sortOrder) {
    state = state.copyWith(sortOrder: sortOrder);
    _debouncedSave();
  }

  void toggleSortOrder() {
    final newOrder = state.sortOrder == SortOrder.ascending
        ? SortOrder.descending
        : SortOrder.ascending;
    state = state.copyWith(sortOrder: newOrder);
    _debouncedSave();
  }

  void clearSearch() {
    state = state.copyWith(searchQuery: '');
    _debouncedSave();
  }

  void clearAllFilters() {
    _saveTimer?.cancel();
    state = const AppSearchState();
    _saveSearchState();
  }
}

// Connection Status
enum ConnectionType { websocket, http, disconnected }

class ConnectionStatus {
  final bool isConnected;
  final ConnectionType connectionType;
  final String? errorMessage;

  const ConnectionStatus({
    required this.isConnected,
    required this.connectionType,
    this.errorMessage,
  });

  ConnectionStatus copyWith({
    bool? isConnected,
    ConnectionType? connectionType,
    String? errorMessage,
  }) {
    return ConnectionStatus(
      isConnected: isConnected ?? this.isConnected,
      connectionType: connectionType ?? this.connectionType,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class ConnectionStatusNotifier extends StateNotifier<ConnectionStatus> {
  TimeTrackerRepository _repository;
  Timer? _statusCheckTimer;
  StreamSubscription<bool>? _connectionStateSubscription;

  ConnectionStatusNotifier(this._repository)
      : super(const ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected
        )) {
    _initializeConnection();
    _setupConnectionStateListener();
  }

  Future<void> _initializeConnection() async {
    if (!mounted) return;

    try {
      await _repository.connect();
      if (mounted) {
        _updateConnectionStatus();
      }
    } catch (e) {
      if (mounted) {
        state = ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected,
          errorMessage: e.toString(),
        );
      }
    }
  }

  void _setupConnectionStateListener() {
    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;

      // Add a small delay to ensure the WebSocket service is fully initialized
      Timer(const Duration(milliseconds: 100), () {
        // Listen to real-time connection state changes
        _connectionStateSubscription = webSocketRepo.connectionStateStream.listen(
          (isConnected) {
            if (mounted) {
              state = ConnectionStatus(
                isConnected: isConnected,
                connectionType: isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
              );
            }
          },
          onError: (error) {
            if (mounted) {
              state = ConnectionStatus(
                isConnected: false,
                connectionType: ConnectionType.disconnected,
                errorMessage: error.toString(),
              );
            }
          },
        );
      });

      // Also set up a fallback timer for initial status check and periodic updates
      _statusCheckTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        _updateConnectionStatus();
      });

      // Set initial state based on current connection
      _updateConnectionStatus();
    } else {
      // For other repository types, use polling
      _startStatusMonitoring();
    }
  }

  void _startStatusMonitoring() {
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _updateConnectionStatus();
    });
  }

  void _updateConnectionStatus() {
    if (!mounted) return;

    if (_repository is WebSocketTimeTrackerRepository) {
      final webSocketRepo = _repository as WebSocketTimeTrackerRepository;
      final isConnected = webSocketRepo.isConnected;

      state = ConnectionStatus(
        isConnected: isConnected,
        connectionType: isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
      );
    } else {
      // For other repository types, assume connected
      state = const ConnectionStatus(
        isConnected: true,
        connectionType: ConnectionType.http,
      );
    }
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();
    super.dispose();
  }

  void updateRepository(TimeTrackerRepository newRepository) {
    if (!mounted) return;

    // Cancel existing subscriptions
    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();

    // Update repository reference
    _repository = newRepository;

    // Set up new connections and listeners with a small delay to avoid race conditions
    Timer(const Duration(milliseconds: 50), () {
      if (mounted) {
        _initializeConnection();
        _setupConnectionStateListener();
      }
    });
  }
}

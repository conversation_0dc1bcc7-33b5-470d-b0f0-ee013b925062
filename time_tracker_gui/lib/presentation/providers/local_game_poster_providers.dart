import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/poster_cache_service.dart';
import '../../data/models/poster_model.dart';
import '../../data/models/app_model.dart';
import 'app_providers.dart';

// Provider for the poster cache service
final posterCacheServiceProvider = Provider<PosterCacheService>((ref) {
  return PosterCacheService();
});

// Provider for getting a cached poster for a specific game
final cachedPosterProvider = FutureProvider.family<Poster?, String>((ref, gameName) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  return await cacheService.searchAndCachePoster(gameName);
});

// Provider for getting cached image path for a specific game
final cachedImagePathProvider = FutureProvider.family<String?, String>((ref, gameName) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  return await cacheService.getCachedImagePath(gameName);
});

// Provider for cache statistics
final cacheStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  final cacheSize = await cacheService.getCacheSize();

  return {
    'size': cacheSize,
    'sizeFormatted': _formatBytes(cacheSize),
  };
});

// Provider to initialize poster caching for all user games
final initializePosterCacheProvider = FutureProvider<void>((ref) async {
  final appsAsyncValue = ref.watch(appsProvider);

  await appsAsyncValue.when(
    data: (apps) async {
      if (apps.isEmpty) {
        print('InitializePosterCache: No apps to cache posters for');
        return;
      }

      final cacheService = ref.read(posterCacheServiceProvider);

      // Filter apps that have valid names for poster lookup
      final validApps = apps.where((app) {
        final gameName = _getGameName(app);
        return gameName.isNotEmpty;
      }).toList();

      if (validApps.isEmpty) {
        print('InitializePosterCache: No valid apps for poster lookup');
        return;
      }

      print('InitializePosterCache: Starting cache initialization for ${validApps.length} apps');

      // Process apps in smaller batches to avoid overwhelming the API
      const batchSize = 3;
      int cachedCount = 0;

      for (int i = 0; i < validApps.length; i += batchSize) {
        final batch = validApps.skip(i).take(batchSize);

        final futures = batch.map((app) async {
          try {
            final gameName = _getGameName(app);

            // Check if already cached
            final existingPoster = await cacheService.getCachedPoster(gameName);
            if (existingPoster != null) {
              return true; // Already cached
            }

            // Use fast method for first batch, regular method for others
            final isFirstBatch = i == 0;
            final poster = isFirstBatch
                ? await cacheService.searchAndCachePosterFast(gameName)
                : await cacheService.searchAndCachePoster(gameName);

            if (poster != null) {
              print('InitializePosterCache: Cached poster for $gameName');
              return true;
            } else {
              print('InitializePosterCache: No poster found for $gameName');
              return false;
            }
          } catch (e) {
            print('InitializePosterCache: Error caching poster for ${_getGameName(app)}: $e');
            return false;
          }
        });

        final results = await Future.wait(futures);
        cachedCount += results.where((success) => success).length;

        // Shorter delay between batches for faster initialization
        if (i + batchSize < validApps.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      print('InitializePosterCache: Completed initialization. Cached posters for $cachedCount/${validApps.length} apps');
    },
    loading: () {
      print('InitializePosterCache: Waiting for apps to load');
    },
    error: (error, stackTrace) {
      print('InitializePosterCache: Error loading apps: $error');
    },
  );
});

// Provider for clearing cache
final clearCacheProvider = FutureProvider<void>((ref) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  await cacheService.clearAllCache();
  
  // Invalidate cache-related providers
  ref.invalidate(cacheStatsProvider);
});

// Helper function to format bytes
String _formatBytes(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}

// Provider for getting poster data for local games
final localGamePostersProvider = FutureProvider.family<Map<int, Poster?>, List<AppModel>>((ref, apps) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  final Map<int, Poster?> posters = {};
  
  // Process games in batches to avoid overwhelming the API
  const batchSize = 5;
  for (int i = 0; i < apps.length; i += batchSize) {
    final batch = apps.skip(i).take(batchSize);
    
    final futures = batch.map((app) async {
      final gameName = _getGameName(app);
      if (gameName.isNotEmpty) {
        final poster = await cacheService.searchAndCachePoster(gameName);
        return MapEntry(app.id, poster);
      }
      return MapEntry(app.id, null);
    });
    
    final results = await Future.wait(futures);
    for (final result in results) {
      posters[result.key] = result.value;
    }
    
    // Add a small delay between batches to be respectful to the API
    if (i + batchSize < apps.length) {
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }
  
  return posters;
});

// Helper function to extract game name from app model
String _getGameName(AppModel app) {
  // Prefer productName over name, and clean it up
  String gameName = '';
  
  if (app.productName?.isNotEmpty == true) {
    gameName = app.productName!;
  } else if (app.name?.isNotEmpty == true) {
    gameName = app.name!;
  }
  
  // Clean up the game name for better search results
  gameName = gameName
      .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '') // Remove .exe
      .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
      .trim();
  
  return gameName;
} 
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/daily_session_service.dart';
import '../../data/models/app_model.dart';
import 'app_providers.dart';

// Provider for today's session data
final todaySessionProvider = StateNotifierProvider<TodaySessionNotifier, AsyncValue<DailySessionData>>((ref) {
  return TodaySessionNotifier(ref);
});

class TodaySessionNotifier extends StateNotifier<AsyncValue<DailySessionData>> {
  final Ref _ref;

  TodaySessionNotifier(this._ref) : super(const AsyncValue.loading()) {
    loadTodayData();
    _listenToTrackingUpdates();
  }

  void _listenToTrackingUpdates() {
    _ref.listen(trackingStatusProvider, (previous, next) {
      next.whenData((current) {
        if (previous != null && previous.hasValue) {
          final prev = previous.value!;
          _handleTrackingStatusChange(prev, current);
        }
      });
    });
  }

  void _handleTrackingStatusChange(TrackingStatus previous, TrackingStatus current) {
    print('Tracking status changed: ${previous.currentSessionDuration} -> ${current.currentSessionDuration}');

    final minutes = current.currentSessionDuration - previous.currentSessionDuration;

    addSession(
      appName: previous.currentApp!,
      durationMinutes: minutes,
      startTime: previous.sessionStartTime ?? DateTime.now(),
    );
  }

  Future<void> loadTodayData() async {
    try {
      state = const AsyncValue.loading();
      final data = await DailySessionService.getTodaySessionData();
      if (mounted) {
        state = AsyncValue.data(data);
      }
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> addSession({
    required String appName,
    required int durationMinutes,
    required DateTime startTime,
  }) async {
    if (durationMinutes <= 0) return;

    try {
      await DailySessionService.addSession(
        appName: appName,
        durationMinutes: durationMinutes,
        startTime: startTime,
      );
      await loadTodayData();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }

  Future<void> refresh() async {
    await loadTodayData();
  }

  Future<void> clearAllData() async {
    try {
      await DailySessionService.clearAllData();
      await loadTodayData();
    } catch (error, stackTrace) {
      if (mounted) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }
}
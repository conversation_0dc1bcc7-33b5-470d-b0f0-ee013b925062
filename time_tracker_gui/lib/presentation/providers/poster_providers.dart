import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/poster_service.dart';
import '../../data/models/poster_model.dart';

final posterServiceProvider = Provider<PosterService>((ref) {
  return PosterService();
});

// Provider for popular/trending games
final postersProvider = FutureProvider<List<Poster>>((ref) async {
  final posterService = ref.read(posterServiceProvider);
  return posterService.fetchPosters();
});

// Provider for search functionality
final searchQueryProvider = StateProvider<String>((ref) => '');

final searchResultsProvider = FutureProvider<List<Poster>>((ref) async {
  final query = ref.watch(searchQueryProvider);
  final posterService = ref.read(posterServiceProvider);
  
  if (query.trim().isEmpty) {
    return posterService.fetchPosters();
  }
  
  return posterService.searchGames(query);
});

// Provider for specific game details
final gameDetailsProvider = FutureProvider.family<Poster?, int>((ref, gameId) async {
  final posterService = ref.read(posterServiceProvider);
  return posterService.getGameById(gameId);
});

// Provider for games by genre
final gamesByGenreProvider = FutureProvider.family<List<Poster>, String>((ref, genreId) async {
  final posterService = ref.read(posterServiceProvider);
  return posterService.getGamesByGenre(genreId);
});

// Provider for pagination state
final currentPageProvider = StateProvider<int>((ref) => 1);

// Provider for paginated results
final paginatedPostersProvider = FutureProvider<List<Poster>>((ref) async {
  final page = ref.watch(currentPageProvider);
  final posterService = ref.read(posterServiceProvider);
  return posterService.fetchPosters(page: page);
}); 
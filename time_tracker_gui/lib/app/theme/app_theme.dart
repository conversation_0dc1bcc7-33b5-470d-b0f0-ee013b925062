import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  // Steam-inspired color palette - made darker
  static const Color steamBlue = Color(0xFF0E1419);
  static const Color steamLightBlue = Color(0xFF1B2838);
  static const Color steamAccent = Color(0xFF66C0F4);
  static const Color steamGreen = Color(0xFF5BA52A);
  static const Color steamYellow = Color(0xFFCBA43C);
  static const Color steamOrange = Color(0xFFD4A948);
  static const Color steamPurple = Color(0xFF8F7FFF);

  // Gaming-focused primary colors
  static const Color primaryColor = steamAccent;
  static const Color primaryLight = Color(0xFF8DD4F7);
  static const Color primaryDark = Color(0xFF4A9FD1);

  static const Color secondaryColor = steamGreen;
  static const Color secondaryLight = Color(0xFF7FC252);
  static const Color secondaryDark = Color(0xFF4A8020);

  static const Color accentColor = steamYellow;
  static const Color accentLight = Color(0xFFE6C866);
  static const Color accentDark = Color(0xFFA88530);

  static const Color errorColor = Color(0xFFE74C3C);
  static const Color successColor = steamGreen;
  static const Color warningColor = steamOrange;
  static const Color infoColor = steamAccent;

  // Much darker Steam-like neutral colors
  static const Color darkestBg = Color(0xFF0A0E13);
  static const Color darkBg = Color(0xFF0E1419);
  static const Color mediumDarkBg = Color(0xFF171A21);
  static const Color lightDarkBg = Color(0xFF1B2838);
  static const Color cardBg = Color(0xFF151A20);
  static const Color lightCardBg = Color(0xFF1E2329);

  // Light theme colors
  static const Color lightBg = Color(0xFFF5F7FA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightCard = Color(0xFFF8FAFC);

  // Neutral grays optimized for gaming UI
  static const Color neutralGray50 = Color(0xFFF8FAFC);
  static const Color neutralGray100 = Color(0xFFF1F5F9);
  static const Color neutralGray200 = Color(0xFFE2E8F0);
  static const Color neutralGray300 = Color(0xFFCBD5E1);
  static const Color neutralGray400 = Color(0xFF94A3B8);
  static const Color neutralGray500 = Color(0xFF64748B);
  static const Color neutralGray600 = Color(0xFF475569);
  static const Color neutralGray700 = Color(0xFF334155);
  static const Color neutralGray800 = Color(0xFF1E293B);
  static const Color neutralGray850 = Color(0xFF0F172A);
  static const Color neutralGray900 = Color(0xFF0F172A);

  // Subtle Steam-inspired gradients - reduced intensity
  static const LinearGradient steamGradient = LinearGradient(
    colors: [steamBlue, Color(0xFF151B22)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [Color(0xFF4A9FD1), Color(0xFF66C0F4)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [cardBg, Color(0xFF181D23)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Enhanced design tokens for gaming UI
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;
  static const double spacingXXXL = 64.0;

  static const double radiusXS = 4.0;
  static const double radiusS = 6.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;
  static const double radiusXXXL = 32.0;

  // Gaming-focused elevation levels
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationFloat = 12.0;

  // Enhanced typography for gaming
  static const double fontSizeXS = 11.0;
  static const double fontSizeS = 13.0;
  static const double fontSizeM = 15.0;
  static const double fontSizeL = 17.0;
  static const double fontSizeXL = 20.0;
  static const double fontSizeXXL = 24.0;
  static const double fontSizeXXXL = 32.0;
  static const double fontSizeHero = 40.0;

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        onPrimary: Colors.white,
        primaryContainer: primaryLight.withOpacity(0.15),
        onPrimaryContainer: primaryDark,
        secondary: secondaryColor,
        onSecondary: Colors.white,
        secondaryContainer: secondaryLight.withOpacity(0.15),
        onSecondaryContainer: secondaryDark,
        tertiary: accentColor,
        onTertiary: Colors.white,
        tertiaryContainer: accentLight.withOpacity(0.15),
        onTertiaryContainer: accentDark,
        error: errorColor,
        onError: Colors.white,
        errorContainer: errorColor.withOpacity(0.15),
        onErrorContainer: errorColor,
        surface: lightSurface,
        onSurface: neutralGray900,
        surfaceContainerHighest: lightCard,
        onSurfaceVariant: neutralGray600,
        outline: neutralGray300,
        outlineVariant: neutralGray200,
        shadow: Colors.black.withOpacity(0.15),
      ),

      // Enhanced typography with gaming feel
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: fontSizeHero,
          fontWeight: FontWeight.w800,
          letterSpacing: -1.0,
          height: 1.1,
        ),
        displayMedium: TextStyle(
          fontSize: fontSizeXXXL,
          fontWeight: FontWeight.w700,
          letterSpacing: -0.5,
          height: 1.2,
        ),
        displaySmall: TextStyle(
          fontSize: fontSizeXXL,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.25,
          height: 1.3,
        ),
        headlineLarge: TextStyle(
          fontSize: fontSizeXL,
          fontWeight: FontWeight.w600,
          letterSpacing: 0,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          letterSpacing: 0,
          height: 1.4,
        ),
        headlineSmall: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.1,
          height: 1.4,
        ),
        titleLarge: TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
          height: 1.4,
        ),
        titleMedium: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.5,
        ),
        titleSmall: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.5,
        ),
        bodyLarge: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.1,
          height: 1.6,
        ),
        bodyMedium: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.2,
          height: 1.6,
        ),
        bodySmall: TextStyle(
          fontSize: fontSizeXS,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          height: 1.6,
        ),
        labelLarge: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
        ),
        labelMedium: TextStyle(
          fontSize: fontSizeXS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
        ),
        labelSmall: TextStyle(
          fontSize: 10.0,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
        ),
      ),

      // Steam-inspired AppBar
      appBarTheme: AppBarTheme(
        elevation: elevationNone,
        scrolledUnderElevation: elevationLow,
        centerTitle: false,
        backgroundColor: lightSurface,
        foregroundColor: neutralGray900,
        surfaceTintColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: const TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          color: neutralGray900,
          letterSpacing: 0,
        ),
        shadowColor: Colors.black.withOpacity(0.1),
      ),

      // Gaming card design
      cardTheme: CardThemeData(
        elevation: elevationLow,
        color: lightCard,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.black.withOpacity(0.08),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusXL),
        ),
        margin: EdgeInsets.zero,
      ),

      // Enhanced button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevationLow,
          shadowColor: Colors.black.withOpacity(0.2),
          padding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          side: const BorderSide(color: neutralGray300, width: 1.5),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingS),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Gaming input design
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: neutralGray100,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: neutralGray300, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: neutralGray300, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
        hintStyle: const TextStyle(color: neutralGray500),
        labelStyle: const TextStyle(color: neutralGray600),
      ),

      dividerTheme: const DividerThemeData(
        thickness: 1,
        space: 1,
        color: neutralGray200,
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        elevation: elevationMedium,
        backgroundColor: lightSurface,
        selectedItemColor: primaryColor,
        unselectedItemColor: neutralGray500,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: TextStyle(fontSize: fontSizeXS, fontWeight: FontWeight.w600),
        unselectedLabelStyle: TextStyle(fontSize: fontSizeXS, fontWeight: FontWeight.w400),
      ),

      navigationRailTheme: const NavigationRailThemeData(
        backgroundColor: lightSurface,
        elevation: elevationLow,
        selectedIconTheme: IconThemeData(color: primaryColor),
        unselectedIconTheme: IconThemeData(color: neutralGray500),
        selectedLabelTextStyle: TextStyle(color: primaryColor, fontWeight: FontWeight.w600),
        unselectedLabelTextStyle: TextStyle(color: neutralGray500, fontWeight: FontWeight.w500),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.dark(
        primary: primaryColor,
        onPrimary: darkBg,
        primaryContainer: primaryColor.withOpacity(0.2),
        onPrimaryContainer: primaryLight,
        secondary: secondaryColor,
        onSecondary: darkBg,
        secondaryContainer: secondaryColor.withOpacity(0.2),
        onSecondaryContainer: secondaryLight,
        tertiary: accentColor,
        onTertiary: darkBg,
        tertiaryContainer: accentColor.withOpacity(0.2),
        onTertiaryContainer: accentLight,
        error: errorColor,
        onError: Colors.white,
        errorContainer: errorColor.withOpacity(0.2),
        onErrorContainer: errorColor,
        surface: darkBg,
        onSurface: Colors.white,
        surfaceContainerHighest: cardBg,
        onSurfaceVariant: neutralGray300,
        outline: neutralGray600,
        outlineVariant: neutralGray700,
        shadow: Colors.black.withOpacity(0.5),
      ),

      // Enhanced dark typography
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: fontSizeHero,
          fontWeight: FontWeight.w800,
          letterSpacing: -1.0,
          height: 1.1,
          color: Colors.white,
        ),
        displayMedium: TextStyle(
          fontSize: fontSizeXXXL,
          fontWeight: FontWeight.w700,
          letterSpacing: -0.5,
          height: 1.2,
          color: Colors.white,
        ),
        displaySmall: TextStyle(
          fontSize: fontSizeXXL,
          fontWeight: FontWeight.w600,
          letterSpacing: -0.25,
          height: 1.3,
          color: Colors.white,
        ),
        headlineLarge: TextStyle(
          fontSize: fontSizeXL,
          fontWeight: FontWeight.w600,
          letterSpacing: 0,
          height: 1.3,
          color: Colors.white,
        ),
        headlineMedium: TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          letterSpacing: 0,
          height: 1.4,
          color: Colors.white,
        ),
        headlineSmall: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.1,
          height: 1.4,
          color: Colors.white,
        ),
        titleLarge: TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
          height: 1.4,
          color: Colors.white,
        ),
        titleMedium: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.5,
          color: Colors.white,
        ),
        titleSmall: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.5,
          color: Colors.white,
        ),
        bodyLarge: TextStyle(
          fontSize: fontSizeM,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.1,
          height: 1.6,
          color: Colors.white,
        ),
        bodyMedium: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.2,
          height: 1.6,
          color: Colors.white,
        ),
        bodySmall: TextStyle(
          fontSize: fontSizeXS,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          height: 1.6,
          color: Colors.white,
        ),
        labelLarge: TextStyle(
          fontSize: fontSizeS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
          color: Colors.white,
        ),
        labelMedium: TextStyle(
          fontSize: fontSizeXS,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
          color: Colors.white,
        ),
        labelSmall: TextStyle(
          fontSize: 10.0,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.4,
          color: Colors.white,
        ),
      ),

      // Steam-like dark AppBar
      appBarTheme: AppBarTheme(
        elevation: elevationNone,
        scrolledUnderElevation: elevationLow,
        centerTitle: false,
        backgroundColor: darkBg,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: const TextStyle(
          fontSize: fontSizeL,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: 0,
        ),
        shadowColor: Colors.black.withOpacity(0.3),
      ),

      // Steam-inspired dark cards
      cardTheme: CardThemeData(
        elevation: elevationLow,
        color: cardBg,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.black.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusXL),
        ),
        margin: EdgeInsets.zero,
      ),

      // Enhanced dark button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevationLow,
          shadowColor: Colors.black.withOpacity(0.5),
          padding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          side: const BorderSide(color: neutralGray600, width: 1.5),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: spacingM, vertical: spacingS),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusL),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeS,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Gaming dark input design
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightCardBg,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: neutralGray600, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: neutralGray600, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusL),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: spacingL, vertical: spacingM),
        hintStyle: const TextStyle(color: neutralGray400),
        labelStyle: const TextStyle(color: neutralGray300),
      ),

      dividerTheme: const DividerThemeData(
        thickness: 1,
        space: 1,
        color: neutralGray700,
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        elevation: elevationMedium,
        backgroundColor: darkBg,
        selectedItemColor: primaryColor,
        unselectedItemColor: neutralGray400,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: TextStyle(fontSize: fontSizeXS, fontWeight: FontWeight.w600),
        unselectedLabelStyle: TextStyle(fontSize: fontSizeXS, fontWeight: FontWeight.w400),
      ),

      navigationRailTheme: const NavigationRailThemeData(
        backgroundColor: darkBg,
        elevation: elevationLow,
        selectedIconTheme: IconThemeData(color: primaryColor),
        unselectedIconTheme: IconThemeData(color: neutralGray400),
        selectedLabelTextStyle: TextStyle(color: primaryColor, fontWeight: FontWeight.w600),
        unselectedLabelTextStyle: TextStyle(color: neutralGray400, fontWeight: FontWeight.w500),
      ),
    );
  }
}

extension AppThemeExtension on ThemeData {
  // Steam color getters
  Color get steamBlue => AppTheme.steamBlue;
  Color get steamLightBlue => AppTheme.steamLightBlue;
  Color get steamAccent => AppTheme.steamAccent;
  Color get steamGreen => AppTheme.steamGreen;
  Color get steamYellow => AppTheme.steamYellow;
  Color get steamOrange => AppTheme.steamOrange;
  Color get steamPurple => AppTheme.steamPurple;

  // Background colors
  Color get darkestBg => AppTheme.darkestBg;
  Color get darkBg => AppTheme.darkBg;
  Color get mediumDarkBg => AppTheme.mediumDarkBg;
  Color get lightDarkBg => AppTheme.lightDarkBg;
  Color get cardBg => AppTheme.cardBg;
  Color get lightCardBg => AppTheme.lightCardBg;

  // Gaming colors
  Color get successColor => AppTheme.successColor;
  Color get warningColor => AppTheme.warningColor;
  Color get infoColor => AppTheme.infoColor;
  Color get accentColor => AppTheme.accentColor;

  // Gradients
  LinearGradient get steamGradient => AppTheme.steamGradient;
  LinearGradient get accentGradient => AppTheme.accentGradient;
  LinearGradient get cardGradient => AppTheme.cardGradient;

  // Design token getters
  double get spacingXS => AppTheme.spacingXS;
  double get spacingS => AppTheme.spacingS;
  double get spacingM => AppTheme.spacingM;
  double get spacingL => AppTheme.spacingL;
  double get spacingXL => AppTheme.spacingXL;
  double get spacingXXL => AppTheme.spacingXXL;
  double get spacingXXXL => AppTheme.spacingXXXL;

  double get radiusXS => AppTheme.radiusXS;
  double get radiusS => AppTheme.radiusS;
  double get radiusM => AppTheme.radiusM;
  double get radiusL => AppTheme.radiusL;
  double get radiusXL => AppTheme.radiusXL;
  double get radiusXXL => AppTheme.radiusXXL;
  double get radiusXXXL => AppTheme.radiusXXXL;

  double get elevationNone => AppTheme.elevationNone;
  double get elevationLow => AppTheme.elevationLow;
  double get elevationMedium => AppTheme.elevationMedium;
  double get elevationHigh => AppTheme.elevationHigh;
  double get elevationFloat => AppTheme.elevationFloat;
}

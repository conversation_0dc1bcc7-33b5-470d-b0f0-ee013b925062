import 'package:flutter/material.dart';

/// Modern color system for the Time Tracker app
/// Maintains the gaming aesthetic while following Material Design 3 principles
class AppColors {
  // ==================== PRIMARY COLORS ====================
  // Steam-inspired blue palette
  static const Color primary50 = Color(0xFFE3F2FD);
  static const Color primary100 = Color(0xFFBBDEFB);
  static const Color primary200 = Color(0xFF90CAF9);
  static const Color primary300 = Color(0xFF64B5F6);
  static const Color primary400 = Color(0xFF42A5F5);
  static const Color primary500 = Color(0xFF66C0F4); // Main Steam blue
  static const Color primary600 = Color(0xFF4A9FD1);
  static const Color primary700 = Color(0xFF1976D2);
  static const Color primary800 = Color(0xFF1565C0);
  static const Color primary900 = Color(0xFF0D47A1);

  // ==================== SECONDARY COLORS ====================
  // Steam green palette
  static const Color secondary50 = Color(0xFFE8F5E8);
  static const Color secondary100 = Color(0xFFC8E6C9);
  static const Color secondary200 = Color(0xFFA5D6A7);
  static const Color secondary300 = Color(0xFF81C784);
  static const Color secondary400 = Color(0xFF66BB6A);
  static const Color secondary500 = Color(0xFF5BA52A); // Steam green
  static const Color secondary600 = Color(0xFF4A8020);
  static const Color secondary700 = Color(0xFF388E3C);
  static const Color secondary800 = Color(0xFF2E7D32);
  static const Color secondary900 = Color(0xFF1B5E20);

  // ==================== TERTIARY COLORS ====================
  // Steam yellow/orange palette
  static const Color tertiary50 = Color(0xFFFFF8E1);
  static const Color tertiary100 = Color(0xFFFFECB3);
  static const Color tertiary200 = Color(0xFFFFE082);
  static const Color tertiary300 = Color(0xFFFFD54F);
  static const Color tertiary400 = Color(0xFFFFCA28);
  static const Color tertiary500 = Color(0xFFCBA43C); // Steam yellow
  static const Color tertiary600 = Color(0xFFA88530);
  static const Color tertiary700 = Color(0xFFF57C00);
  static const Color tertiary800 = Color(0xFFEF6C00);
  static const Color tertiary900 = Color(0xFFE65100);

  // ==================== NEUTRAL COLORS ====================
  // Improved neutral palette for better contrast and accessibility
  static const Color neutral0 = Color(0xFFFFFFFF);
  static const Color neutral50 = Color(0xFFF8FAFC);
  static const Color neutral100 = Color(0xFFF1F5F9);
  static const Color neutral200 = Color(0xFFE2E8F0);
  static const Color neutral300 = Color(0xFFCBD5E1);
  static const Color neutral400 = Color(0xFF94A3B8);
  static const Color neutral500 = Color(0xFF64748B);
  static const Color neutral600 = Color(0xFF475569);
  static const Color neutral700 = Color(0xFF334155);
  static const Color neutral800 = Color(0xFF1E293B);
  static const Color neutral900 = Color(0xFF0F172A);
  static const Color neutral950 = Color(0xFF020617);

  // ==================== DARK THEME SURFACES ====================
  // Steam-inspired dark surfaces with better contrast
  static const Color darkSurface0 = Color(0xFF0A0E13);   // Darkest background
  static const Color darkSurface1 = Color(0xFF0E1419);   // Main background
  static const Color darkSurface2 = Color(0xFF151A20);   // Card background
  static const Color darkSurface3 = Color(0xFF1B2838);   // Elevated surface
  static const Color darkSurface4 = Color(0xFF1E2329);   // Higher elevation
  static const Color darkSurface5 = Color(0xFF252A31);   // Highest elevation

  // ==================== SEMANTIC COLORS ====================
  static const Color success = Color(0xFF10B981);
  static const Color successLight = Color(0xFF34D399);
  static const Color successDark = Color(0xFF059669);

  static const Color warning = Color(0xFFF59E0B);
  static const Color warningLight = Color(0xFFFBBF24);
  static const Color warningDark = Color(0xFFD97706);

  static const Color error = Color(0xFFEF4444);
  static const Color errorLight = Color(0xFFF87171);
  static const Color errorDark = Color(0xFFDC2626);

  static const Color info = primary500;
  static const Color infoLight = primary400;
  static const Color infoDark = primary600;

  // ==================== SPECIAL GAMING COLORS ====================
  static const Color steamBlue = primary500;
  static const Color steamGreen = secondary500;
  static const Color steamYellow = tertiary500;
  static const Color steamOrange = Color(0xFFD4A948);
  static const Color steamPurple = Color(0xFF8F7FFF);

  // ==================== OPACITY LEVELS ====================
  static const double opacity10 = 0.1;
  static const double opacity20 = 0.2;
  static const double opacity30 = 0.3;
  static const double opacity40 = 0.4;
  static const double opacity50 = 0.5;
  static const double opacity60 = 0.6;
  static const double opacity70 = 0.7;
  static const double opacity80 = 0.8;
  static const double opacity90 = 0.9;

  // ==================== GRADIENTS ====================
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary400, primary600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary400, secondary600],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient darkGradient = LinearGradient(
    colors: [darkSurface1, darkSurface2],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient heroGradient = LinearGradient(
    colors: [
      Color(0xFF1B2838),
      Color(0xFF151A20),
      Color(0xFF0E1419),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // ==================== HELPER METHODS ====================
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color blend(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  // Get appropriate text color for background
  static Color getTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? neutral900 : neutral0;
  }

  // Get appropriate contrast color
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? neutral900 : neutral0;
  }
}

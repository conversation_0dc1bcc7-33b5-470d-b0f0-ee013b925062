import 'package:flutter/material.dart';

/// Modern design tokens following Material Design 3 principles
/// with gaming-inspired customizations
class DesignTokens {
  // ==================== SPACING SYSTEM ====================
  // 4px base unit for consistent spacing
  static const double space0 = 0.0;
  static const double space1 = 4.0;   // 4px
  static const double space2 = 8.0;   // 8px
  static const double space3 = 12.0;  // 12px
  static const double space4 = 16.0;  // 16px
  static const double space5 = 20.0;  // 20px
  static const double space6 = 24.0;  // 24px
  static const double space8 = 32.0;  // 32px
  static const double space10 = 40.0; // 40px
  static const double space12 = 48.0; // 48px
  static const double space16 = 64.0; // 64px
  static const double space20 = 80.0; // 80px

  // Semantic spacing aliases
  static const double spacingXS = space1;
  static const double spacingS = space2;
  static const double spacingM = space4;
  static const double spacingL = space6;
  static const double spacingXL = space8;
  static const double spacingXXL = space12;

  // ==================== BORDER RADIUS SYSTEM ====================
  static const double radius0 = 0.0;
  static const double radius1 = 4.0;
  static const double radius2 = 8.0;
  static const double radius3 = 12.0;
  static const double radius4 = 16.0;
  static const double radius5 = 20.0;
  static const double radius6 = 24.0;
  static const double radiusFull = 9999.0;

  // Semantic radius aliases
  static const double radiusXS = radius1;
  static const double radiusS = radius2;
  static const double radiusM = radius3;
  static const double radiusL = radius4;
  static const double radiusXL = radius5;
  static const double radiusXXL = radius6;

  // ==================== ELEVATION SYSTEM ====================
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 3.0;
  static const double elevation3 = 6.0;
  static const double elevation4 = 8.0;
  static const double elevation5 = 12.0;

  // ==================== TYPOGRAPHY SCALE ====================
  static const double fontSize10 = 10.0;
  static const double fontSize12 = 12.0;
  static const double fontSize14 = 14.0;
  static const double fontSize16 = 16.0;
  static const double fontSize18 = 18.0;
  static const double fontSize20 = 20.0;
  static const double fontSize24 = 24.0;
  static const double fontSize28 = 28.0;
  static const double fontSize32 = 32.0;
  static const double fontSize36 = 36.0;
  static const double fontSize48 = 48.0;

  // ==================== ICON SIZES ====================
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;

  // ==================== COMPONENT SIZES ====================
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  static const double inputHeight = 48.0;
  static const double cardMinHeight = 80.0;
  static const double appBarHeight = 56.0;

  // ==================== RESPONSIVE BREAKPOINTS ====================
  static const double breakpointMobile = 600.0;
  static const double breakpointTablet = 900.0;
  static const double breakpointDesktop = 1200.0;
  static const double breakpointWide = 1600.0;

  // ==================== LAYOUT CONSTRAINTS ====================
  static const double maxContentWidth = 1200.0;
  static const double sidebarWidth = 280.0;
  static const double navigationRailWidth = 80.0;

  // ==================== ANIMATION DURATIONS ====================
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationNormal = Duration(milliseconds: 250);
  static const Duration durationSlow = Duration(milliseconds: 400);
  static const Duration durationSlower = Duration(milliseconds: 600);

  // ==================== TOUCH TARGETS ====================
  static const double minTouchTarget = 44.0;
  static const double recommendedTouchTarget = 48.0;

  // ==================== GAMING-SPECIFIC TOKENS ====================
  static const double cardElevationLow = elevation1;
  static const double cardElevationMedium = elevation2;
  static const double cardElevationHigh = elevation3;
  
  static const double glassBlur = 10.0;
  static const double heroBlur = 15.0;
  
  static const double borderWidthThin = 1.0;
  static const double borderWidthMedium = 2.0;
  static const double borderWidthThick = 3.0;

  // ==================== RESPONSIVE HELPERS ====================
  static bool isMobile(double width) => width < breakpointMobile;
  static bool isTablet(double width) => width >= breakpointMobile && width < breakpointDesktop;
  static bool isDesktop(double width) => width >= breakpointDesktop;
  static bool isWide(double width) => width >= breakpointWide;

  static int getGridColumns(double width) {
    if (width < breakpointMobile) return 1;
    if (width < breakpointTablet) return 2;
    if (width < breakpointDesktop) return 3;
    if (width < breakpointWide) return 4;
    return 6;
  }

  static double getContentPadding(double width) {
    if (width < breakpointMobile) return spacingM;
    if (width < breakpointDesktop) return spacingL;
    return spacingXL;
  }
}

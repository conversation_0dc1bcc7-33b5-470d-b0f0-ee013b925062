import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Modern theme system following Material Design 3 principles
/// with gaming-inspired customizations
class AppThemeV2 {
  static ThemeData get lightTheme {
    const colorScheme = ColorScheme.light(
      // Primary colors
      primary: AppColors.primary500,
      onPrimary: AppColors.neutral0,
      primaryContainer: AppColors.primary100,
      onPrimaryContainer: AppColors.primary900,
      
      // Secondary colors
      secondary: AppColors.secondary500,
      onSecondary: AppColors.neutral0,
      secondaryContainer: AppColors.secondary100,
      onSecondaryContainer: AppColors.secondary900,
      
      // Tertiary colors
      tertiary: AppColors.tertiary500,
      onTertiary: AppColors.neutral0,
      tertiaryContainer: AppColors.tertiary100,
      onTertiaryContainer: AppColors.tertiary900,
      
      // Error colors
      error: AppColors.error,
      onError: AppColors.neutral0,
      errorContainer: AppColors.errorLight,
      onErrorContainer: AppColors.errorDark,
      
      // Surface colors
      surface: AppColors.neutral0,
      onSurface: AppColors.neutral900,
      surfaceContainerLowest: AppColors.neutral50,
      surfaceContainerLow: AppColors.neutral100,
      surfaceContainer: AppColors.neutral200,
      surfaceContainerHigh: AppColors.neutral300,
      surfaceContainerHighest: AppColors.neutral400,
      
      // Outline colors
      outline: AppColors.neutral400,
      outlineVariant: AppColors.neutral300,
      
      // Other colors
      shadow: AppColors.neutral900,
      scrim: AppColors.neutral900,
      inverseSurface: AppColors.neutral800,
      onInverseSurface: AppColors.neutral100,
      inversePrimary: AppColors.primary200,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: _buildTextTheme(colorScheme),
      
      // App Bar
      appBarTheme: _buildAppBarTheme(colorScheme, false),
      
      // Card
      cardTheme: _buildCardTheme(colorScheme, false),
      
      // Elevated Button
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      
      // Outlined Button
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      
      // Text Button
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      
      // Input Decoration
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, false),
      
      // Bottom Navigation Bar
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(colorScheme, false),
      
      // Navigation Rail
      navigationRailTheme: _buildNavigationRailTheme(colorScheme, false),
      
      // Chip
      chipTheme: _buildChipTheme(colorScheme, false),
      

      
      // Divider
      dividerTheme: _buildDividerTheme(colorScheme),
      
      // Icon
      iconTheme: _buildIconTheme(colorScheme, false),
      
      // List Tile
      listTileTheme: _buildListTileTheme(colorScheme, false),
    );
  }

  static ThemeData get darkTheme {
    const colorScheme = ColorScheme.dark(
      // Primary colors
      primary: AppColors.primary400,
      onPrimary: AppColors.neutral900,
      primaryContainer: AppColors.primary800,
      onPrimaryContainer: AppColors.primary100,
      
      // Secondary colors
      secondary: AppColors.secondary400,
      onSecondary: AppColors.neutral900,
      secondaryContainer: AppColors.secondary800,
      onSecondaryContainer: AppColors.secondary100,
      
      // Tertiary colors
      tertiary: AppColors.tertiary400,
      onTertiary: AppColors.neutral900,
      tertiaryContainer: AppColors.tertiary800,
      onTertiaryContainer: AppColors.tertiary100,
      
      // Error colors
      error: AppColors.errorLight,
      onError: AppColors.neutral900,
      errorContainer: AppColors.errorDark,
      onErrorContainer: AppColors.errorLight,
      
      // Surface colors
      surface: AppColors.darkSurface1,
      onSurface: AppColors.neutral100,
      surfaceContainerLowest: AppColors.darkSurface0,
      surfaceContainerLow: AppColors.darkSurface1,
      surfaceContainer: AppColors.darkSurface2,
      surfaceContainerHigh: AppColors.darkSurface3,
      surfaceContainerHighest: AppColors.darkSurface4,
      
      // Outline colors
      outline: AppColors.neutral600,
      outlineVariant: AppColors.neutral700,
      
      // Other colors
      shadow: AppColors.neutral950,
      scrim: AppColors.neutral950,
      inverseSurface: AppColors.neutral200,
      onInverseSurface: AppColors.neutral800,
      inversePrimary: AppColors.primary600,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: _buildTextTheme(colorScheme),
      
      // App Bar
      appBarTheme: _buildAppBarTheme(colorScheme, true),
      
      // Card
      cardTheme: _buildCardTheme(colorScheme, true),
      
      // Elevated Button
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      
      // Outlined Button
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      
      // Text Button
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      
      // Input Decoration
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme, true),
      
      // Bottom Navigation Bar
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(colorScheme, true),
      
      // Navigation Rail
      navigationRailTheme: _buildNavigationRailTheme(colorScheme, true),
      
      // Chip
      chipTheme: _buildChipTheme(colorScheme, true),
      

      
      // Divider
      dividerTheme: _buildDividerTheme(colorScheme),
      
      // Icon
      iconTheme: _buildIconTheme(colorScheme, true),
      
      // List Tile
      listTileTheme: _buildListTileTheme(colorScheme, true),
    );
  }

  // Helper methods for building theme components
  static TextTheme _buildTextTheme(ColorScheme colorScheme) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: DesignTokens.fontSize48,
        fontWeight: FontWeight.w800,
        letterSpacing: -1.0,
        height: 1.1,
        color: colorScheme.onSurface,
      ),
      displayMedium: TextStyle(
        fontSize: DesignTokens.fontSize36,
        fontWeight: FontWeight.w700,
        letterSpacing: -0.5,
        height: 1.2,
        color: colorScheme.onSurface,
      ),
      displaySmall: TextStyle(
        fontSize: DesignTokens.fontSize32,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.25,
        height: 1.3,
        color: colorScheme.onSurface,
      ),
      headlineLarge: TextStyle(
        fontSize: DesignTokens.fontSize28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        height: 1.3,
        color: colorScheme.onSurface,
      ),
      headlineMedium: TextStyle(
        fontSize: DesignTokens.fontSize24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        height: 1.4,
        color: colorScheme.onSurface,
      ),
      headlineSmall: TextStyle(
        fontSize: DesignTokens.fontSize20,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.1,
        height: 1.4,
        color: colorScheme.onSurface,
      ),
      titleLarge: TextStyle(
        fontSize: DesignTokens.fontSize18,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        height: 1.4,
        color: colorScheme.onSurface,
      ),
      titleMedium: TextStyle(
        fontSize: DesignTokens.fontSize16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.5,
        color: colorScheme.onSurface,
      ),
      titleSmall: TextStyle(
        fontSize: DesignTokens.fontSize14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.5,
        color: colorScheme.onSurface,
      ),
      bodyLarge: TextStyle(
        fontSize: DesignTokens.fontSize16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.1,
        height: 1.5,
        color: colorScheme.onSurface,
      ),
      bodyMedium: TextStyle(
        fontSize: DesignTokens.fontSize14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.2,
        height: 1.5,
        color: colorScheme.onSurface,
      ),
      bodySmall: TextStyle(
        fontSize: DesignTokens.fontSize12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        height: 1.5,
        color: colorScheme.onSurface,
      ),
      labelLarge: TextStyle(
        fontSize: DesignTokens.fontSize14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        height: 1.4,
        color: colorScheme.onSurface,
      ),
      labelMedium: TextStyle(
        fontSize: DesignTokens.fontSize12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.3,
        color: colorScheme.onSurface,
      ),
      labelSmall: TextStyle(
        fontSize: DesignTokens.fontSize10,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        height: 1.3,
        color: colorScheme.onSurface,
      ),
    );
  }

  static AppBarTheme _buildAppBarTheme(ColorScheme colorScheme, bool isDark) {
    return AppBarTheme(
      elevation: DesignTokens.elevation0,
      scrolledUnderElevation: DesignTokens.elevation1,
      centerTitle: false,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      surfaceTintColor: Colors.transparent,
      systemOverlayStyle: isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
      titleTextStyle: TextStyle(
        fontSize: DesignTokens.fontSize20,
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
        letterSpacing: 0,
      ),
      shadowColor: colorScheme.shadow.withValues(alpha: 0.3),
    );
  }

  static CardThemeData _buildCardTheme(ColorScheme colorScheme, bool isDark) {
    return CardThemeData(
      elevation: DesignTokens.cardElevationLow,
      color: colorScheme.surfaceContainer,
      surfaceTintColor: Colors.transparent,
      shadowColor: colorScheme.shadow.withValues(alpha: isDark ? 0.3 : 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
      ),
      margin: EdgeInsets.zero,
    );
  }

  static ElevatedButtonThemeData _buildElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: DesignTokens.elevation1,
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        minimumSize: const Size(0, DesignTokens.buttonHeightM),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        ),
        textStyle: const TextStyle(
          fontSize: DesignTokens.fontSize14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  static OutlinedButtonThemeData _buildOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        minimumSize: const Size(0, DesignTokens.buttonHeightM),
        side: BorderSide(color: colorScheme.outline),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        ),
        textStyle: const TextStyle(
          fontSize: DesignTokens.fontSize14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  static TextButtonThemeData _buildTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        minimumSize: const Size(0, DesignTokens.buttonHeightM),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        ),
        textStyle: const TextStyle(
          fontSize: DesignTokens.fontSize14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  static InputDecorationTheme _buildInputDecorationTheme(ColorScheme colorScheme, bool isDark) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceContainerLow,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
    );
  }

  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme(ColorScheme colorScheme, bool isDark) {
    return BottomNavigationBarThemeData(
      type: BottomNavigationBarType.fixed,
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      elevation: DesignTokens.elevation2,
    );
  }

  static NavigationRailThemeData _buildNavigationRailTheme(ColorScheme colorScheme, bool isDark) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      selectedIconTheme: IconThemeData(color: colorScheme.primary),
      unselectedIconTheme: IconThemeData(color: colorScheme.onSurfaceVariant),
      selectedLabelTextStyle: TextStyle(color: colorScheme.primary),
      unselectedLabelTextStyle: TextStyle(color: colorScheme.onSurfaceVariant),
    );
  }

  static ChipThemeData _buildChipTheme(ColorScheme colorScheme, bool isDark) {
    return ChipThemeData(
      backgroundColor: colorScheme.surfaceContainerLow,
      selectedColor: colorScheme.primaryContainer,
      labelStyle: TextStyle(color: colorScheme.onSurface),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
      ),
    );
  }



  static DividerThemeData _buildDividerTheme(ColorScheme colorScheme) {
    return DividerThemeData(
      color: colorScheme.outlineVariant,
      thickness: 1,
      space: 1,
    );
  }

  static IconThemeData _buildIconTheme(ColorScheme colorScheme, bool isDark) {
    return IconThemeData(
      color: colorScheme.onSurface,
      size: DesignTokens.iconM,
    );
  }

  static ListTileThemeData _buildListTileTheme(ColorScheme colorScheme, bool isDark) {
    return ListTileThemeData(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Modern navigation components following Material Design 3 principles
/// with gaming-inspired customizations

class ModernNavigationBar extends StatelessWidget {
  const ModernNavigationBar({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemTapped,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.showLabels = true,
    this.type = BottomNavigationBarType.fixed,
  });

  final List<ModernNavigationItem> items;
  final int selectedIndex;
  final ValueChanged<int> onItemTapped;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final bool showLabels;
  final BottomNavigationBarType type;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? (isDark ? AppColors.darkSurface2 : AppColors.neutral0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: DesignTokens.cardElevationMedium,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingM,
            vertical: DesignTokens.spacingS,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == selectedIndex;

              return _NavigationBarItem(
                item: item,
                isSelected: isSelected,
                onTap: () => onItemTapped(index),
                selectedColor: selectedItemColor ?? theme.colorScheme.primary,
                unselectedColor: unselectedItemColor ?? theme.colorScheme.onSurfaceVariant,
                showLabel: showLabels,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

class _NavigationBarItem extends StatelessWidget {
  const _NavigationBarItem({
    required this.item,
    required this.isSelected,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
    required this.showLabel,
  });

  final ModernNavigationItem item;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;
  final bool showLabel;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = isSelected ? selectedColor : unselectedColor;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.radiusM),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingS,
              vertical: DesignTokens.spacingM,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.all(DesignTokens.spacingS),
                  decoration: BoxDecoration(
                    color: isSelected ? selectedColor.withValues(alpha: 0.1) : Colors.transparent,
                    borderRadius: BorderRadius.circular(DesignTokens.radiusS),
                  ),
                  child: Icon(
                    isSelected ? item.selectedIcon : item.icon,
                    color: color,
                    size: DesignTokens.iconM,
                  ),
                ),
                if (showLabel) ...[
                  const SizedBox(height: DesignTokens.spacingXS),
                  Text(
                    item.label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: color,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ModernNavigationItem {
  const ModernNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.badge,
  });

  final IconData icon;
  final IconData? selectedIcon;
  final String label;
  final Widget? badge;
}

class ModernTabBar extends StatelessWidget implements PreferredSizeWidget {
  const ModernTabBar({
    super.key,
    required this.tabs,
    this.controller,
    this.isScrollable = false,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
    this.indicatorWeight = 3.0,
  });

  final List<Widget> tabs;
  final TabController? controller;
  final bool isScrollable;
  final Color? indicatorColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;
  final double indicatorWeight;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TabBar(
      controller: controller,
      tabs: tabs,
      isScrollable: isScrollable,
      indicatorColor: indicatorColor ?? theme.colorScheme.primary,
      labelColor: labelColor ?? theme.colorScheme.primary,
      unselectedLabelColor: unselectedLabelColor ?? theme.colorScheme.onSurfaceVariant,
      indicatorWeight: indicatorWeight,
      indicatorSize: TabBarIndicatorSize.tab,
      labelStyle: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w400,
      ),
      splashFactory: InkRipple.splashFactory,
      overlayColor: WidgetStateProperty.all(
        theme.colorScheme.primary.withValues(alpha: 0.1),
      ),
    );
  }
}

class ModernTab extends StatelessWidget {
  const ModernTab({
    super.key,
    this.text,
    this.icon,
    this.child,
    this.height,
  });

  final String? text;
  final Widget? icon;
  final Widget? child;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Tab(
      text: text,
      icon: icon,
      child: child,
      height: height,
    );
  }
}

class ModernSideNavigation extends StatelessWidget {
  const ModernSideNavigation({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemTapped,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.width = 280,
    this.isCollapsed = false,
  });

  final List<ModernNavigationItem> items;
  final int selectedIndex;
  final ValueChanged<int> onItemTapped;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double width;
  final bool isCollapsed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isCollapsed ? 72 : width,
      decoration: BoxDecoration(
        color: backgroundColor ?? (isDark ? AppColors.darkSurface2 : AppColors.neutral0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: DesignTokens.cardElevationMedium,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(height: DesignTokens.spacingL),
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = index == selectedIndex;

            return _SideNavigationItem(
              item: item,
              isSelected: isSelected,
              isCollapsed: isCollapsed,
              onTap: () => onItemTapped(index),
              selectedColor: selectedItemColor ?? theme.colorScheme.primary,
              unselectedColor: unselectedItemColor ?? theme.colorScheme.onSurfaceVariant,
            );
          }),
        ],
      ),
    );
  }
}

class _SideNavigationItem extends StatelessWidget {
  const _SideNavigationItem({
    required this.item,
    required this.isSelected,
    required this.isCollapsed,
    required this.onTap,
    required this.selectedColor,
    required this.unselectedColor,
  });

  final ModernNavigationItem item;
  final bool isSelected;
  final bool isCollapsed;
  final VoidCallback onTap;
  final Color selectedColor;
  final Color unselectedColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = isSelected ? selectedColor : unselectedColor;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingS,
        vertical: DesignTokens.spacingXS,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.radiusM),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacingM,
              vertical: DesignTokens.spacingM,
            ),
            decoration: BoxDecoration(
              color: isSelected ? selectedColor.withValues(alpha: 0.1) : Colors.transparent,
              borderRadius: BorderRadius.circular(DesignTokens.radiusM),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? item.selectedIcon ?? item.icon : item.icon,
                  color: color,
                  size: DesignTokens.iconM,
                ),
                if (!isCollapsed) ...[
                  const SizedBox(width: DesignTokens.spacingM),
                  Expanded(
                    child: Text(
                      item.label,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: color,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Base card component following Material Design 3 principles
/// with gaming-inspired customizations
class BaseCard extends StatelessWidget {
  const BaseCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.elevation,
    this.shadowColor,
    this.onTap,
    this.variant = CardVariant.elevated,
    this.size = CardSize.medium,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  final double? elevation;
  final Color? shadowColor;
  final VoidCallback? onTap;
  final CardVariant variant;
  final CardSize size;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: margin ?? _getDefaultMargin(),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: _getBorderRadius(),
          child: Container(
            padding: padding ?? _getDefaultPadding(),
            decoration: _getDecoration(theme, isDark),
            child: child,
          ),
        ),
      ),
    );
  }

  EdgeInsetsGeometry _getDefaultMargin() {
    return switch (size) {
      CardSize.small => EdgeInsets.all(DesignTokens.spacingS),
      CardSize.medium => EdgeInsets.all(DesignTokens.spacingM),
      CardSize.large => EdgeInsets.all(DesignTokens.spacingL),
    };
  }

  EdgeInsetsGeometry _getDefaultPadding() {
    return switch (size) {
      CardSize.small => EdgeInsets.all(DesignTokens.spacingM),
      CardSize.medium => EdgeInsets.all(DesignTokens.spacingL),
      CardSize.large => EdgeInsets.all(DesignTokens.spacingXL),
    };
  }

  BorderRadius _getBorderRadius() {
    return borderRadius ?? 
      switch (size) {
        CardSize.small => BorderRadius.circular(DesignTokens.radiusS),
        CardSize.medium => BorderRadius.circular(DesignTokens.radiusM),
        CardSize.large => BorderRadius.circular(DesignTokens.radiusL),
      };
  }

  BoxDecoration _getDecoration(ThemeData theme, bool isDark) {
    return switch (variant) {
      CardVariant.elevated => _getElevatedDecoration(theme, isDark),
      CardVariant.outlined => _getOutlinedDecoration(theme, isDark),
      CardVariant.filled => _getFilledDecoration(theme, isDark),
      CardVariant.glass => _getGlassDecoration(theme, isDark),
    };
  }

  BoxDecoration _getElevatedDecoration(ThemeData theme, bool isDark) {
    return BoxDecoration(
      color: backgroundColor ?? (isDark ? AppColors.darkSurface2 : AppColors.neutral0),
      borderRadius: _getBorderRadius(),
      boxShadow: [
        BoxShadow(
          color: (shadowColor ?? Colors.black).withOpacity(isDark ? 0.3 : 0.1),
          blurRadius: elevation ?? DesignTokens.cardElevationMedium,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  BoxDecoration _getOutlinedDecoration(ThemeData theme, bool isDark) {
    return BoxDecoration(
      color: backgroundColor ?? Colors.transparent,
      borderRadius: _getBorderRadius(),
      border: Border.all(
        color: borderColor ?? (isDark ? AppColors.neutral700 : AppColors.neutral300),
        width: borderWidth ?? DesignTokens.borderWidthThin,
      ),
    );
  }

  BoxDecoration _getFilledDecoration(ThemeData theme, bool isDark) {
    return BoxDecoration(
      color: backgroundColor ?? (isDark ? AppColors.darkSurface3 : AppColors.neutral100),
      borderRadius: _getBorderRadius(),
    );
  }

  BoxDecoration _getGlassDecoration(ThemeData theme, bool isDark) {
    return BoxDecoration(
      color: (backgroundColor ?? (isDark ? AppColors.darkSurface2 : AppColors.neutral0))
          .withOpacity(isDark ? 0.3 : 0.8),
      borderRadius: _getBorderRadius(),
      border: Border.all(
        color: (borderColor ?? (isDark ? AppColors.primary500 : AppColors.neutral300))
            .withOpacity(0.5),
        width: borderWidth ?? DesignTokens.borderWidthThin,
      ),
    );
  }
}

/// Card variant types
enum CardVariant {
  elevated,  // Default Material Design 3 elevated card
  outlined,  // Outlined card with border
  filled,    // Filled card with background color
  glass,     // Glass morphism effect
}

/// Card size variants
enum CardSize {
  small,
  medium,
  large,
}

/// Specialized card components
class StatsCard extends BaseCard {
  const StatsCard({
    super.key,
    required super.child,
    super.onTap,
    this.accentColor,
  }) : super(
    variant: CardVariant.elevated,
    size: CardSize.medium,
  );

  final Color? accentColor;
}

class HeroCard extends BaseCard {
  const HeroCard({
    super.key,
    required super.child,
    super.onTap,
  }) : super(
    variant: CardVariant.glass,
    size: CardSize.large,
  );
}

class InfoCard extends BaseCard {
  const InfoCard({
    super.key,
    required super.child,
    super.onTap,
  }) : super(
    variant: CardVariant.filled,
    size: CardSize.medium,
  );
}

class ActionCard extends BaseCard {
  const ActionCard({
    super.key,
    required super.child,
    required super.onTap,
  }) : super(
    variant: CardVariant.outlined,
    size: CardSize.medium,
  );
}

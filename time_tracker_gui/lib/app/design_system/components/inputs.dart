import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Modern input components following Material Design 3 principles
/// with gaming-inspired customizations

enum InputVariant {
  outlined,
  filled,
  underlined,
}

enum InputSize {
  small,
  medium,
  large,
}

class ModernTextField extends StatelessWidget {
  const ModernTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.validator,
    this.inputFormatters,
    this.variant = InputVariant.outlined,
    this.size = InputSize.medium,
    this.autofocus = false,
    this.focusNode,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final InputVariant variant;
  final InputSize size;
  final bool autofocus;
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      enabled: enabled,
      readOnly: readOnly,
      maxLines: maxLines,
      maxLength: maxLength,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      onTap: onTap,
      validator: validator,
      inputFormatters: inputFormatters,
      autofocus: autofocus,
      focusNode: focusNode,
      style: _getTextStyle(theme),
      decoration: _getInputDecoration(theme, isDark),
    );
  }

  TextStyle _getTextStyle(ThemeData theme) {
    return switch (size) {
      InputSize.small => theme.textTheme.bodySmall ?? const TextStyle(),
      InputSize.medium => theme.textTheme.bodyMedium ?? const TextStyle(),
      InputSize.large => theme.textTheme.bodyLarge ?? const TextStyle(),
    };
  }

  InputDecoration _getInputDecoration(ThemeData theme, bool isDark) {
    final baseDecoration = InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      contentPadding: _getContentPadding(),
      counterText: maxLength != null ? null : '',
    );

    return switch (variant) {
      InputVariant.outlined => baseDecoration.copyWith(
        border: _getOutlinedBorder(theme, isDark),
        enabledBorder: _getOutlinedBorder(theme, isDark),
        focusedBorder: _getOutlinedBorder(theme, isDark, focused: true),
        errorBorder: _getOutlinedBorder(theme, isDark, error: true),
        focusedErrorBorder: _getOutlinedBorder(theme, isDark, error: true, focused: true),
        filled: false,
      ),
      InputVariant.filled => baseDecoration.copyWith(
        border: _getFilledBorder(theme, isDark),
        enabledBorder: _getFilledBorder(theme, isDark),
        focusedBorder: _getFilledBorder(theme, isDark, focused: true),
        errorBorder: _getFilledBorder(theme, isDark, error: true),
        focusedErrorBorder: _getFilledBorder(theme, isDark, error: true, focused: true),
        filled: true,
        fillColor: isDark ? AppColors.darkSurface2 : AppColors.neutral50,
      ),
      InputVariant.underlined => baseDecoration.copyWith(
        border: _getUnderlineBorder(theme, isDark),
        enabledBorder: _getUnderlineBorder(theme, isDark),
        focusedBorder: _getUnderlineBorder(theme, isDark, focused: true),
        errorBorder: _getUnderlineBorder(theme, isDark, error: true),
        focusedErrorBorder: _getUnderlineBorder(theme, isDark, error: true, focused: true),
        filled: false,
      ),
    };
  }

  EdgeInsetsGeometry _getContentPadding() {
    return switch (size) {
      InputSize.small => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
      InputSize.medium => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingL,
        vertical: DesignTokens.spacingM,
      ),
      InputSize.large => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingL,
        vertical: DesignTokens.spacingL,
      ),
    };
  }

  OutlineInputBorder _getOutlinedBorder(ThemeData theme, bool isDark, {bool focused = false, bool error = false}) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = theme.colorScheme.error;
      borderWidth = DesignTokens.borderWidthThick;
    } else if (focused) {
      borderColor = theme.colorScheme.primary;
      borderWidth = DesignTokens.borderWidthThick;
    } else {
      borderColor = isDark ? AppColors.neutral600 : AppColors.neutral300;
      borderWidth = DesignTokens.borderWidthThin;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(_getBorderRadius()),
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  OutlineInputBorder _getFilledBorder(ThemeData theme, bool isDark, {bool focused = false, bool error = false}) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = theme.colorScheme.error;
      borderWidth = DesignTokens.borderWidthThick;
    } else if (focused) {
      borderColor = theme.colorScheme.primary;
      borderWidth = DesignTokens.borderWidthThick;
    } else {
      borderColor = Colors.transparent;
      borderWidth = DesignTokens.borderWidthThin;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(_getBorderRadius()),
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  UnderlineInputBorder _getUnderlineBorder(ThemeData theme, bool isDark, {bool focused = false, bool error = false}) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = theme.colorScheme.error;
      borderWidth = DesignTokens.borderWidthThick;
    } else if (focused) {
      borderColor = theme.colorScheme.primary;
      borderWidth = DesignTokens.borderWidthThick;
    } else {
      borderColor = isDark ? AppColors.neutral600 : AppColors.neutral300;
      borderWidth = DesignTokens.borderWidthThin;
    }

    return UnderlineInputBorder(
      borderSide: BorderSide(
        color: borderColor,
        width: borderWidth,
      ),
    );
  }

  double _getBorderRadius() {
    return switch (size) {
      InputSize.small => DesignTokens.radiusS,
      InputSize.medium => DesignTokens.radiusM,
      InputSize.large => DesignTokens.radiusL,
    };
  }
}

/// Specialized input components
class SearchField extends ModernTextField {
  const SearchField({
    super.key,
    super.controller,
    super.hintText = 'Search...',
    super.onChanged,
    super.onSubmitted,
    this.onClear,
  }) : super(
    prefixIcon: const Icon(Icons.search),
    variant: InputVariant.filled,
    textInputAction: TextInputAction.search,
  );

  final VoidCallback? onClear;

  @override
  Widget build(BuildContext context) {
    return ModernTextField(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      prefixIcon: const Icon(Icons.search),
      suffixIcon: controller?.text.isNotEmpty == true
          ? IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () {
                controller?.clear();
                onClear?.call();
              },
            )
          : null,
      variant: variant,
      textInputAction: textInputAction,
    );
  }
}

class PasswordField extends StatefulWidget {
  const PasswordField({
    super.key,
    this.controller,
    this.labelText = 'Password',
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.variant = InputVariant.outlined,
    this.size = InputSize.medium,
  });

  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final String? Function(String?)? validator;
  final InputVariant variant;
  final InputSize size;

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    return ModernTextField(
      controller: widget.controller,
      labelText: widget.labelText,
      hintText: widget.hintText,
      onChanged: widget.onChanged,
      onSubmitted: widget.onSubmitted,
      validator: widget.validator,
      obscureText: _obscureText,
      prefixIcon: const Icon(Icons.lock_outline),
      suffixIcon: IconButton(
        icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: () => setState(() => _obscureText = !_obscureText),
      ),
      variant: widget.variant,
      size: widget.size,
    );
  }
}

import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Modern indicator components for status, progress, and feedback
/// Following Material Design 3 principles with gaming-inspired customizations

enum BadgeVariant {
  primary,
  secondary,
  success,
  warning,
  error,
  info,
}

enum BadgeSize {
  small,
  medium,
  large,
}

class ModernBadge extends StatelessWidget {
  const ModernBadge({
    super.key,
    required this.child,
    this.variant = BadgeVariant.primary,
    this.size = BadgeSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.showBorder = false,
  });

  final Widget child;
  final BadgeVariant variant;
  final BadgeSize size;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: backgroundColor ?? _getBackgroundColor(theme, isDark),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: showBorder
            ? Border.all(
                color: borderColor ?? _getBorderColor(theme, isDark),
                width: DesignTokens.borderWidthThin,
              )
            : null,
      ),
      child: DefaultTextStyle(
        style: _getTextStyle(theme, isDark),
        child: child,
      ),
    );
  }

  EdgeInsetsGeometry _getPadding() {
    return switch (size) {
      BadgeSize.small => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingS,
        vertical: DesignTokens.spacingXS,
      ),
      BadgeSize.medium => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
      BadgeSize.large => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingL,
        vertical: DesignTokens.spacingM,
      ),
    };
  }

  double _getBorderRadius() {
    return switch (size) {
      BadgeSize.small => DesignTokens.radiusS,
      BadgeSize.medium => DesignTokens.radiusM,
      BadgeSize.large => DesignTokens.radiusL,
    };
  }

  Color _getBackgroundColor(ThemeData theme, bool isDark) {
    return switch (variant) {
      BadgeVariant.primary => theme.colorScheme.primary,
      BadgeVariant.secondary => theme.colorScheme.secondary,
      BadgeVariant.success => AppColors.success,
      BadgeVariant.warning => AppColors.warning,
      BadgeVariant.error => theme.colorScheme.error,
      BadgeVariant.info => AppColors.info,
    };
  }

  Color _getBorderColor(ThemeData theme, bool isDark) {
    return _getBackgroundColor(theme, isDark).withValues(alpha: 0.3);
  }

  Color _getTextColor(ThemeData theme, bool isDark) {
    return switch (variant) {
      BadgeVariant.primary => theme.colorScheme.onPrimary,
      BadgeVariant.secondary => theme.colorScheme.onSecondary,
      BadgeVariant.success => AppColors.neutral0,
      BadgeVariant.warning => AppColors.neutral900,
      BadgeVariant.error => theme.colorScheme.onError,
      BadgeVariant.info => AppColors.neutral0,
    };
  }

  TextStyle _getTextStyle(ThemeData theme, bool isDark) {
    final baseStyle = switch (size) {
      BadgeSize.small => theme.textTheme.bodySmall,
      BadgeSize.medium => theme.textTheme.bodyMedium,
      BadgeSize.large => theme.textTheme.bodyLarge,
    };

    return (baseStyle ?? const TextStyle()).copyWith(
      color: textColor ?? _getTextColor(theme, isDark),
      fontWeight: FontWeight.w600,
    );
  }
}

class StatusIndicator extends StatelessWidget {
  const StatusIndicator({
    super.key,
    required this.status,
    this.size = 12.0,
    this.showPulse = false,
  });

  final StatusType status;
  final double size;
  final bool showPulse;

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(status);

    Widget indicator = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );

    if (showPulse) {
      indicator = _PulsingIndicator(
        color: color,
        size: size,
        child: indicator,
      );
    }

    return indicator;
  }

  Color _getStatusColor(StatusType status) {
    return switch (status) {
      StatusType.online => AppColors.success,
      StatusType.offline => AppColors.neutral500,
      StatusType.away => AppColors.warning,
      StatusType.busy => AppColors.error,
      StatusType.tracking => AppColors.primary500,
      StatusType.paused => AppColors.warning,
    };
  }
}

class _PulsingIndicator extends StatefulWidget {
  const _PulsingIndicator({
    required this.color,
    required this.size,
    required this.child,
  });

  final Color color;
  final double size;
  final Widget child;

  @override
  State<_PulsingIndicator> createState() => _PulsingIndicatorState();
}

class _PulsingIndicatorState extends State<_PulsingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: widget.size * (1 + _animation.value * 0.5),
              height: widget.size * (1 + _animation.value * 0.5),
              decoration: BoxDecoration(
                color: widget.color.withValues(alpha: 0.3 * (1 - _animation.value)),
                shape: BoxShape.circle,
              ),
            ),
            widget.child,
          ],
        );
      },
    );
  }
}

enum StatusType {
  online,
  offline,
  away,
  busy,
  tracking,
  paused,
}

class ModernProgressIndicator extends StatelessWidget {
  const ModernProgressIndicator({
    super.key,
    required this.value,
    this.backgroundColor,
    this.valueColor,
    this.height = 8.0,
    this.borderRadius,
    this.showPercentage = false,
  });

  final double value; // 0.0 to 1.0
  final Color? backgroundColor;
  final Color? valueColor;
  final double height;
  final BorderRadius? borderRadius;
  final bool showPercentage;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final effectiveBackgroundColor = backgroundColor ??
        (isDark ? AppColors.neutral700 : AppColors.neutral200);
    final effectiveValueColor = valueColor ?? theme.colorScheme.primary;
    final effectiveBorderRadius = borderRadius ??
        BorderRadius.circular(height / 2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: height,
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: effectiveBorderRadius,
          ),
          child: ClipRRect(
            borderRadius: effectiveBorderRadius,
            child: LinearProgressIndicator(
              value: value.clamp(0.0, 1.0),
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(effectiveValueColor),
            ),
          ),
        ),
        if (showPercentage) ...[
          const SizedBox(height: DesignTokens.spacingXS),
          Text(
            '${(value * 100).round()}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }
}

class LoadingSpinner extends StatelessWidget {
  const LoadingSpinner({
    super.key,
    this.size = 24.0,
    this.color,
    this.strokeWidth = 3.0,
  });

  final double size;
  final Color? color;
  final double strokeWidth;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? theme.colorScheme.primary,
        ),
      ),
    );
  }
}

class NotificationDot extends StatelessWidget {
  const NotificationDot({
    super.key,
    this.count,
    this.showDot = true,
    this.color,
    this.size = 8.0,
  });

  final int? count;
  final bool showDot;
  final Color? color;
  final double size;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (!showDot && count == null) return const SizedBox.shrink();

    final effectiveColor = color ?? theme.colorScheme.error;

    if (count != null && count! > 0) {
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignTokens.spacingXS,
          vertical: 2,
        ),
        decoration: BoxDecoration(
          color: effectiveColor,
          borderRadius: BorderRadius.circular(size),
        ),
        constraints: BoxConstraints(
          minWidth: size * 2,
          minHeight: size * 2,
        ),
        child: Text(
          count! > 99 ? '99+' : count.toString(),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onError,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: effectiveColor,
        shape: BoxShape.circle,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../app_colors.dart';

/// Modern button components following Material Design 3 principles
/// with gaming-inspired customizations

enum ButtonVariant {
  primary,
  secondary,
  outlined,
  text,
  ghost,
  danger,
}

enum ButtonSize {
  small,
  medium,
  large,
}

class ModernButton extends StatelessWidget {
  const ModernButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconPosition = IconPosition.leading,
    this.fullWidth = false,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final ButtonVariant variant;
  final ButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final IconData? icon;
  final IconPosition iconPosition;
  final bool fullWidth;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final effectiveOnPressed = (isDisabled || isLoading) ? null : onPressed;
    
    Widget buttonChild = _buildButtonContent(theme);
    
    if (fullWidth) {
      buttonChild = SizedBox(
        width: double.infinity,
        child: buttonChild,
      );
    }

    return switch (variant) {
      ButtonVariant.primary => _buildPrimaryButton(theme, isDark, effectiveOnPressed, buttonChild),
      ButtonVariant.secondary => _buildSecondaryButton(theme, isDark, effectiveOnPressed, buttonChild),
      ButtonVariant.outlined => _buildOutlinedButton(theme, isDark, effectiveOnPressed, buttonChild),
      ButtonVariant.text => _buildTextButton(theme, isDark, effectiveOnPressed, buttonChild),
      ButtonVariant.ghost => _buildGhostButton(theme, isDark, effectiveOnPressed, buttonChild),
      ButtonVariant.danger => _buildDangerButton(theme, isDark, effectiveOnPressed, buttonChild),
    };
  }

  Widget _buildButtonContent(ThemeData theme) {
    if (isLoading) {
      return SizedBox(
        height: _getIconSize(),
        width: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getContentColor(theme, variant),
          ),
        ),
      );
    }

    final List<Widget> children = [];
    
    if (icon != null && iconPosition == IconPosition.leading) {
      children.add(Icon(icon, size: _getIconSize()));
      children.add(SizedBox(width: _getSpacing()));
    }
    
    children.add(child);
    
    if (icon != null && iconPosition == IconPosition.trailing) {
      children.add(SizedBox(width: _getSpacing()));
      children.add(Icon(icon, size: _getIconSize()));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  Widget _buildPrimaryButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        elevation: _getElevation(),
      ),
      child: child,
    );
  }

  Widget _buildSecondaryButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.secondary,
        foregroundColor: theme.colorScheme.onSecondary,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        elevation: _getElevation(),
      ),
      child: child,
    );
  }

  Widget _buildOutlinedButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: theme.colorScheme.primary,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        side: BorderSide(
          color: theme.colorScheme.primary,
          width: DesignTokens.borderWidthThin,
        ),
      ),
      child: child,
    );
  }

  Widget _buildTextButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: theme.colorScheme.primary,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
      ),
      child: child,
    );
  }

  Widget _buildGhostButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: theme.colorScheme.onSurfaceVariant,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        backgroundColor: Colors.transparent,
      ),
      child: child,
    );
  }

  Widget _buildDangerButton(ThemeData theme, bool isDark, VoidCallback? onPressed, Widget child) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.error,
        foregroundColor: theme.colorScheme.onError,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        ),
        elevation: _getElevation(),
      ),
      child: child,
    );
  }

  EdgeInsetsGeometry _getPadding() {
    return switch (size) {
      ButtonSize.small => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingM,
        vertical: DesignTokens.spacingS,
      ),
      ButtonSize.medium => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingL,
        vertical: DesignTokens.spacingM,
      ),
      ButtonSize.large => const EdgeInsets.symmetric(
        horizontal: DesignTokens.spacingXL,
        vertical: DesignTokens.spacingL,
      ),
    };
  }

  double _getBorderRadius() {
    return switch (size) {
      ButtonSize.small => DesignTokens.radiusS,
      ButtonSize.medium => DesignTokens.radiusM,
      ButtonSize.large => DesignTokens.radiusL,
    };
  }

  double _getElevation() {
    return switch (size) {
      ButtonSize.small => DesignTokens.cardElevationLow,
      ButtonSize.medium => DesignTokens.cardElevationMedium,
      ButtonSize.large => DesignTokens.cardElevationHigh,
    };
  }

  double _getIconSize() {
    return switch (size) {
      ButtonSize.small => DesignTokens.iconS,
      ButtonSize.medium => DesignTokens.iconM,
      ButtonSize.large => DesignTokens.iconL,
    };
  }

  double _getSpacing() {
    return switch (size) {
      ButtonSize.small => DesignTokens.spacingXS,
      ButtonSize.medium => DesignTokens.spacingS,
      ButtonSize.large => DesignTokens.spacingM,
    };
  }

  Color _getContentColor(ThemeData theme, ButtonVariant variant) {
    return switch (variant) {
      ButtonVariant.primary => theme.colorScheme.onPrimary,
      ButtonVariant.secondary => theme.colorScheme.onSecondary,
      ButtonVariant.outlined => theme.colorScheme.primary,
      ButtonVariant.text => theme.colorScheme.primary,
      ButtonVariant.ghost => theme.colorScheme.onSurfaceVariant,
      ButtonVariant.danger => theme.colorScheme.onError,
    };
  }
}

enum IconPosition {
  leading,
  trailing,
}

/// Specialized button components
class PrimaryButton extends ModernButton {
  const PrimaryButton({
    super.key,
    required super.onPressed,
    required super.child,
    super.size,
    super.isLoading,
    super.isDisabled,
    super.icon,
    super.iconPosition,
    super.fullWidth,
  }) : super(variant: ButtonVariant.primary);
}

class SecondaryButton extends ModernButton {
  const SecondaryButton({
    super.key,
    required super.onPressed,
    required super.child,
    super.size,
    super.isLoading,
    super.isDisabled,
    super.icon,
    super.iconPosition,
    super.fullWidth,
  }) : super(variant: ButtonVariant.secondary);
}

class OutlinedModernButton extends ModernButton {
  const OutlinedModernButton({
    super.key,
    required super.onPressed,
    required super.child,
    super.size,
    super.isLoading,
    super.isDisabled,
    super.icon,
    super.iconPosition,
    super.fullWidth,
  }) : super(variant: ButtonVariant.outlined);
}

class DangerButton extends ModernButton {
  const DangerButton({
    super.key,
    required super.onPressed,
    required super.child,
    super.size,
    super.isLoading,
    super.isDisabled,
    super.icon,
    super.iconPosition,
    super.fullWidth,
  }) : super(variant: ButtonVariant.danger);
}

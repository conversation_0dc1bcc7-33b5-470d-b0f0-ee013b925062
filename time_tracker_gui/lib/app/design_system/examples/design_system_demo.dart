import 'package:flutter/material.dart';
import '../design_system.dart';

/// Demo page showcasing the design system components
/// This serves as both documentation and testing for the components
class DesignSystemDemo extends StatefulWidget {
  const DesignSystemDemo({super.key});

  @override
  State<DesignSystemDemo> createState() => _DesignSystemDemoState();
}

class _DesignSystemDemoState extends State<DesignSystemDemo>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedNavIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Design System Demo'),
        bottom: ModernTabBar(
          controller: _tabController,
          tabs: const [
            ModernTab(text: 'Cards'),
            ModernTab(text: 'Buttons'),
            ModernTab(text: 'Inputs'),
            ModernTab(text: 'Indicators'),
            ModernTab(text: 'Layout'),
            ModernTab(text: 'Navigation'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCardsDemo(),
          _buildButtonsDemo(),
          _buildInputsDemo(),
          _buildIndicatorsDemo(),
          _buildLayoutDemo(),
          _buildNavigationDemo(),
        ],
      ),
    );
  }

  Widget _buildCardsDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cards',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          ModernRow(
            spacing: DesignTokens.spacingM,
            wrap: true,
            children: [
              SizedBox(
                width: 300,
                child: ModernStatsCard(
                  title: 'Total Sessions',
                  value: '42',
                  subtitle: 'This week',
                  icon: Icons.play_circle_outline,
                  accentColor: AppColors.primary500,
                ),
              ),
              SizedBox(
                width: 300,
                child: ModernInfoCard(
                  title: 'Recent Activity',
                  icon: Icons.timeline,
                  child: const Text('Your recent gaming sessions and achievements.'),
                ),
              ),
            ],
          ),
          ModernHeroCard(
            gradient: AppColors.primaryGradient,
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to Time Tracker',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: DesignTokens.spacingM),
                Text(
                  'Track your gaming sessions with style',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Buttons',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          ModernRow(
            spacing: DesignTokens.spacingM,
            wrap: true,
            children: [
              PrimaryButton(
                onPressed: () {},
                child: const Text('Primary'),
              ),
              SecondaryButton(
                onPressed: () {},
                child: const Text('Secondary'),
              ),
              OutlinedModernButton(
                onPressed: () {},
                child: const Text('Outlined'),
              ),
              DangerButton(
                onPressed: () {},
                child: const Text('Danger'),
              ),
            ],
          ),
          ModernRow(
            spacing: DesignTokens.spacingM,
            wrap: true,
            children: [
              PrimaryButton(
                onPressed: () {},
                icon: Icons.play_arrow,
                child: const Text('With Icon'),
              ),
              PrimaryButton(
                onPressed: null,
                child: const Text('Disabled'),
              ),
              PrimaryButton(
                onPressed: () {},
                isLoading: true,
                child: const Text('Loading'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInputsDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Inputs',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const ModernTextField(
            labelText: 'Username',
            hintText: 'Enter your username',
            prefixIcon: Icon(Icons.person),
          ),
          const PasswordField(
            labelText: 'Password',
            hintText: 'Enter your password',
          ),
          const SearchField(
            hintText: 'Search games...',
          ),
          const ModernTextField(
            labelText: 'Description',
            hintText: 'Enter description',
            maxLines: 3,
            variant: InputVariant.filled,
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorsDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Indicators',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          ModernRow(
            spacing: DesignTokens.spacingM,
            wrap: true,
            children: [
              const ModernBadge(
                variant: BadgeVariant.primary,
                child: Text('Primary'),
              ),
              const ModernBadge(
                variant: BadgeVariant.success,
                child: Text('Success'),
              ),
              const ModernBadge(
                variant: BadgeVariant.warning,
                child: Text('Warning'),
              ),
              const ModernBadge(
                variant: BadgeVariant.error,
                child: Text('Error'),
              ),
            ],
          ),
          ModernRow(
            spacing: DesignTokens.spacingM,
            children: [
              const Text('Status: '),
              const StatusIndicator(status: StatusType.online, showPulse: true),
              const SizedBox(width: DesignTokens.spacingS),
              const Text('Online'),
            ],
          ),
          const ModernProgressIndicator(
            value: 0.7,
            showPercentage: true,
          ),
          const ModernRow(
            spacing: DesignTokens.spacingM,
            children: [
              LoadingSpinner(),
              Text('Loading...'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLayoutDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Layout',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          ModernSection(
            title: 'Section Title',
            subtitle: 'Section subtitle with description',
            showDivider: true,
            child: const Text('Section content goes here.'),
          ),
          ModernContainer(
            backgroundColor: AppColors.primary50,
            borderColor: AppColors.primary500,
            borderWidth: 2,
            padding: const EdgeInsets.all(DesignTokens.spacingL),
            child: const Text('Container with custom styling'),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationDemo() {
    return ModernColumn(
      children: [
        Expanded(
          child: Center(
            child: Text(
              'Navigation Demo\nSelected: $_selectedNavIndex',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ),
        ),
        ModernNavigationBar(
          selectedIndex: _selectedNavIndex,
          onItemTapped: (index) => setState(() => _selectedNavIndex = index),
          items: const [
            ModernNavigationItem(
              icon: Icons.home_outlined,
              selectedIcon: Icons.home,
              label: 'Home',
            ),
            ModernNavigationItem(
              icon: Icons.apps_outlined,
              selectedIcon: Icons.apps,
              label: 'Apps',
            ),
            ModernNavigationItem(
              icon: Icons.timeline_outlined,
              selectedIcon: Icons.timeline,
              label: 'Timeline',
            ),
            ModernNavigationItem(
              icon: Icons.settings_outlined,
              selectedIcon: Icons.settings,
              label: 'Settings',
            ),
          ],
        ),
      ],
    );
  }
}

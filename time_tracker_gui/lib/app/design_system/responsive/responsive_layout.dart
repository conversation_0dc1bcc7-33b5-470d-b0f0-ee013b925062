import 'package:flutter/material.dart';
import '../design_tokens.dart';

/// Simplified responsive layout system that replaces complex ScreenUtil usage
/// Uses Flutter's built-in responsive widgets for better maintainability
class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.wide,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? wide;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        if (DesignTokens.isWide(width) && wide != null) {
          return wide!;
        } else if (DesignTokens.isDesktop(width) && desktop != null) {
          return desktop!;
        } else if (DesignTokens.isTablet(width) && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// Responsive grid that automatically adjusts columns based on screen size
class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = DesignTokens.spacingM,
    this.runSpacing,
    this.maxCrossAxisExtent = 300.0,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  final List<Widget> children;
  final double spacing;
  final double? runSpacing;
  final double maxCrossAxisExtent;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.all(DesignTokens.spacingM),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: maxCrossAxisExtent,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: spacing,
          mainAxisSpacing: runSpacing ?? spacing,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// Responsive columns that stack on mobile and flow horizontally on larger screens
class ResponsiveColumns extends StatelessWidget {
  const ResponsiveColumns({
    super.key,
    required this.children,
    this.spacing = DesignTokens.spacingM,
    this.breakpoint = DesignTokens.breakpointTablet,
  });

  final List<Widget> children;
  final double spacing;
  final double breakpoint;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < breakpoint) {
          // Stack vertically on mobile
          return Column(
            children: children
                .expand((child) => [child, SizedBox(height: spacing)])
                .take(children.length * 2 - 1)
                .toList(),
          );
        } else {
          // Flow horizontally on larger screens
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children
                .expand((child) => [Expanded(child: child), SizedBox(width: spacing)])
                .take(children.length * 2 - 1)
                .toList(),
          );
        }
      },
    );
  }
}

/// Responsive container with max width and centered content
class ResponsiveContainer extends StatelessWidget {
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth = DesignTokens.maxContentWidth,
    this.padding,
  });

  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: Padding(
          padding: padding ?? _getResponsivePadding(context),
          child: child,
        ),
      ),
    );
  }

  EdgeInsetsGeometry _getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return EdgeInsets.symmetric(
      horizontal: DesignTokens.getContentPadding(width),
    );
  }
}

/// Responsive spacing that adjusts based on screen size
class ResponsiveSpacing extends StatelessWidget {
  const ResponsiveSpacing({
    super.key,
    this.mobile = DesignTokens.spacingM,
    this.tablet,
    this.desktop,
  });

  final double mobile;
  final double? tablet;
  final double? desktop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        if (DesignTokens.isDesktop(width) && desktop != null) {
          return SizedBox(height: desktop!);
        } else if (DesignTokens.isTablet(width) && tablet != null) {
          return SizedBox(height: tablet!);
        } else {
          return SizedBox(height: mobile);
        }
      },
    );
  }
}

/// Responsive text that adjusts size based on screen size
class ResponsiveText extends StatelessWidget {
  const ResponsiveText(
    this.text, {
    super.key,
    this.mobileStyle,
    this.tabletStyle,
    this.desktopStyle,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  final String text;
  final TextStyle? mobileStyle;
  final TextStyle? tabletStyle;
  final TextStyle? desktopStyle;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        TextStyle? style;
        if (DesignTokens.isDesktop(width) && desktopStyle != null) {
          style = desktopStyle;
        } else if (DesignTokens.isTablet(width) && tabletStyle != null) {
          style = tabletStyle;
        } else {
          style = mobileStyle;
        }
        
        return Text(
          text,
          style: style,
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}

/// Helper extension for responsive values
extension ResponsiveExtension on BuildContext {
  bool get isMobile => DesignTokens.isMobile(MediaQuery.of(this).size.width);
  bool get isTablet => DesignTokens.isTablet(MediaQuery.of(this).size.width);
  bool get isDesktop => DesignTokens.isDesktop(MediaQuery.of(this).size.width);
  bool get isWide => DesignTokens.isWide(MediaQuery.of(this).size.width);
  
  double get screenWidth => MediaQuery.of(this).size.width;
  double get screenHeight => MediaQuery.of(this).size.height;
  
  int get gridColumns => DesignTokens.getGridColumns(screenWidth);
  double get contentPadding => DesignTokens.getContentPadding(screenWidth);
}
